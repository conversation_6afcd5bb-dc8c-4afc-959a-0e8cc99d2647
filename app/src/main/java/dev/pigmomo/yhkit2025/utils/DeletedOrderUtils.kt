package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.api.model.order.Order
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.model.DeletedOrderEntity
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 删除订单记录工具类
 * 提供便捷的删除订单记录查询和统计功能
 */
object DeletedOrderUtils {
    
    private const val TAG = "DeletedOrderUtils"
    
    /**
     * 获取所有删除订单记录
     */
    fun getAllDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getAllDeletedOrders()
    }
    
    /**
     * 根据账号UID获取删除订单记录
     */
    fun getDeletedOrdersByUid(context: Context, uid: String): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getDeletedOrdersByUid(uid)
    }
    
    /**
     * 根据手机号获取删除订单记录
     */
    fun getDeletedOrdersByPhoneNumber(context: Context, phoneNumber: String): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getDeletedOrdersByPhoneNumber(phoneNumber)
    }
    
    /**
     * 获取今日删除的订单记录
     */
    fun getTodayDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfDay = calendar.timeInMillis
        
        calendar.add(Calendar.DAY_OF_MONTH, 1)
        val startOfNextDay = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfDay, startOfNextDay)
    }
    
    /**
     * 获取本周删除的订单记录
     */
    fun getThisWeekDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        
        // 设置为本周第一天（周一）
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfWeek = calendar.timeInMillis
        
        // 设置为下周第一天
        calendar.add(Calendar.WEEK_OF_YEAR, 1)
        val startOfNextWeek = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfWeek, startOfNextWeek)
    }
    
    /**
     * 获取本月删除的订单记录
     */
    fun getThisMonthDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        
        // 设置为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfMonth = calendar.timeInMillis
        
        // 设置为下月第一天
        calendar.add(Calendar.MONTH, 1)
        val startOfNextMonth = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfMonth, startOfNextMonth)
    }
    
    /**
     * 检查订单是否已被删除
     */
    suspend fun isOrderDeleted(context: Context, orderId: String): Boolean {
        return try {
            val database = AppDatabase.getDatabase(context)
            database.deletedOrderDao().isOrderDeleted(orderId)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check if order is deleted: ${e.message}")
            false
        }
    }
    
    /**
     * 获取删除订单统计信息
     */
    suspend fun getDeletedOrderStatistics(context: Context): DeletedOrderStatistics {
        return try {
            val database = AppDatabase.getDatabase(context)
            val dao = database.deletedOrderDao()
            
            val totalCount = dao.getDeletedOrderCount()
            
            // 获取今日删除数量
            val calendar = Calendar.getInstance()
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val startOfNextDay = calendar.timeInMillis
            val todayCount = dao.getDeletedOrderCountByDateRange(startOfDay, startOfNextDay)
            
            // 获取本周删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfWeek = calendar.timeInMillis
            calendar.add(Calendar.WEEK_OF_YEAR, 1)
            val startOfNextWeek = calendar.timeInMillis
            val thisWeekCount = dao.getDeletedOrderCountByDateRange(startOfWeek, startOfNextWeek)
            
            // 获取本月删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_MONTH, 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfMonth = calendar.timeInMillis
            calendar.add(Calendar.MONTH, 1)
            val startOfNextMonth = calendar.timeInMillis
            val thisMonthCount = dao.getDeletedOrderCountByDateRange(startOfMonth, startOfNextMonth)
            
            DeletedOrderStatistics(
                totalCount = totalCount,
                todayCount = todayCount,
                thisWeekCount = thisWeekCount,
                thisMonthCount = thisMonthCount
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get deleted order statistics: ${e.message}")
            DeletedOrderStatistics()
        }
    }
    
    /**
     * 获取指定账号的删除订单统计信息
     */
    suspend fun getDeletedOrderStatisticsByUid(context: Context, uid: String): DeletedOrderStatistics {
        return try {
            val database = AppDatabase.getDatabase(context)
            val dao = database.deletedOrderDao()
            
            val totalCount = dao.getDeletedOrderCountByUid(uid)
            
            // 获取今日删除数量
            val calendar = Calendar.getInstance()
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val startOfNextDay = calendar.timeInMillis
            val todayRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfDay, startOfNextDay).first()
            val todayCount = todayRecords.size
            
            // 获取本周删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfWeek = calendar.timeInMillis
            calendar.add(Calendar.WEEK_OF_YEAR, 1)
            val startOfNextWeek = calendar.timeInMillis
            val thisWeekRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfWeek, startOfNextWeek).first()
            val thisWeekCount = thisWeekRecords.size
            
            // 获取本月删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_MONTH, 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfMonth = calendar.timeInMillis
            calendar.add(Calendar.MONTH, 1)
            val startOfNextMonth = calendar.timeInMillis
            val thisMonthRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfMonth, startOfNextMonth).first()
            val thisMonthCount = thisMonthRecords.size
            
            DeletedOrderStatistics(
                totalCount = totalCount,
                todayCount = todayCount,
                thisWeekCount = thisWeekCount,
                thisMonthCount = thisMonthCount
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get deleted order statistics by uid: ${e.message}")
            DeletedOrderStatistics()
        }
    }
    
    /**
     * 格式化删除时间
     */
    fun formatDeleteTime(timestamp: Long): String {
        return try {
            val date = Date(timestamp)
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            formatter.format(date)
        } catch (e: Exception) {
            "时间格式错误"
        }
    }
    
    /**
     * 清理指定时间之前的删除记录（用于数据清理）
     */
    suspend fun cleanupOldRecords(context: Context, daysToKeep: Int = 30): Int {
        return try {
            val database = AppDatabase.getDatabase(context)
            val calendar = Calendar.getInstance()
            calendar.add(Calendar.DAY_OF_MONTH, -daysToKeep)
            val cutoffTime = calendar.timeInMillis

            database.deletedOrderDao().deleteDeletedOrdersBeforeTimestamp(cutoffTime)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup old records: ${e.message}")
            0
        }
    }

    /**
     * 备份已删除的订单数据到文件
     * @param context 上下文
     * @param order 订单对象
     * @param token 账号信息
     * @param extraNote 额外备注信息
     * @return 备份文件路径，失败时返回null
     */
    fun backupDeletedOrderData(
        context: Context,
        order: Order,
        token: OrderTokenEntity,
        extraNote: String = ""
    ): String? {
        return try {
            // 创建备份数据的JSON对象
            val backupData = createOrderBackupJson(order, token, extraNote)

            // 生成文件名（包含时间戳和订单ID）
            val timestamp = DateUtils.formatDateTime(System.currentTimeMillis(), "yyyyMMdd_HHmmss")
            val fileName = "deleted_order_${order.id}_${timestamp}.json"

            // 保存到指定的备份目录
            val filePath = FileUtils.saveTextToPublicDownloads(
                context,
                fileName,
                backupData.toString(2), // 格式化JSON，缩进2个空格
                "BackupDeleteOrders"
            ) ?: FileUtils.saveTextToDownloads(
                context,
                fileName,
                backupData.toString(2),
                "BackupDeleteOrders"
            )

            if (filePath != null) {
                Log.d(TAG, "Order backup saved to: $filePath")
            } else {
                Log.e(TAG, "Failed to save order backup for order: ${order.id}")
            }

            filePath
        } catch (e: Exception) {
            Log.e(TAG, "Error backing up deleted order data: ${e.message}", e)
            null
        }
    }

    /**
     * 创建订单备份的JSON数据
     * @param order 订单对象
     * @param token 账号信息
     * @param extraNote 额外备注信息
     * @return JSON对象
     */
    private fun createOrderBackupJson(
        order: Order,
        token: OrderTokenEntity,
        extraNote: String
    ): JSONObject {
        val backupJson = JSONObject()

        // 备份基本信息
        backupJson.put("backupInfo", JSONObject().apply {
            put("backupTimestamp", System.currentTimeMillis())
            put("backupDate", SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date()))
            put("extraNote", extraNote)
        })

        // 备份账号信息
        backupJson.put("accountInfo", JSONObject().apply {
            put("uid", token.uid)
            put("phoneNumber", token.phoneNumber)
            put("userKey", token.userKey)
        })

        // 备份订单基本信息
        backupJson.put("orderInfo", JSONObject().apply {
            put("id", order.id)
            put("status", order.status)
            put("title", order.title)
            put("subtitle", order.subtitle)
            put("description", order.description)
            put("dinners", order.dinners)
            put("totalpayment", order.totalpayment)
            put("totalamount", order.totalamount)
            put("balanceamount", order.balanceamount)
            put("balancepay", order.balancepay)
            put("paytypename", order.paytypename)
            put("shopid", order.shopid)
            put("ispickself", order.ispickself)
            put("deliverymode", order.deliverymode)
            put("ordersubtype", order.ordersubtype)
            put("statusmsg", order.statusmsg)
            put("refundable", order.refundable)
            put("canrate", order.canrate)
            put("canbuyagain", order.canbuyagain)
            put("ispresale", order.ispresale)
            put("isDeliveryException", order.isDeliveryException)
            put("mergeOrder", order.mergeOrder)
            put("canAppendOrder", order.canAppendOrder)
            put("ordertypetag", order.ordertypetag)
            put("icon", order.icon)
            put("detailaction", order.detailaction)
            put("cashierswitch", order.cashierswitch)
        })

        // 备份时间信息
        backupJson.put("timeInfo", JSONObject().apply {
            put("generate", order.timeinfo.generate)
            put("pay", order.timeinfo.pay)
            put("pick", order.timeinfo.pick)
            put("delivery", order.timeinfo.delivery)
            put("complete", order.timeinfo.complete)
            put("refunding", order.timeinfo.refunding)
            put("refunded", order.timeinfo.refunded)
            put("payend", order.timeinfo.payend)
        })

        // 备份商家信息
        backupJson.put("sellerInfo", JSONObject().apply {
            put("id", order.seller.id)
            put("title", order.seller.title)
            put("icon", order.seller.icon)
        })

        // 备份商品信息
        val productsArray = org.json.JSONArray()
        order.products.forEach { product ->
            val productJson = JSONObject().apply {
                put("id", product.id)
                put("title", product.title)
                put("subtitle", product.subtitle)
                put("action", product.action)
                put("pattern", product.pattern)
                put("unit", product.unit)
                put("qty", product.qty)
                put("num", product.num)
                put("barcode", product.barcode)
                put("rowNum", product.rowNum)
                put("imgurl", product.imgurl)
                put("calnum", product.calnum)
                put("isrefund", product.isrefund)
                put("isbulkitem", product.isbulkitem)
                put("goodstag", product.goodstag)
                put("sellerid", product.sellerid)
                put("skusaletype", product.skusaletype)
                put("isperformancehourhour", product.isperformancehourhour)
                put("performanceHourHour", product.performanceHourHour)
                put("deliveryRuleId", product.deliveryRuleId)
                put("canNotBuy", product.canNotBuy)
                put("bundlepromocode", product.bundlepromocode)
                put("originalselectstate", product.originalselectstate)
                put("lackOrExchangeNotShow", product.lackOrExchangeNotShow)

                // 备份价格信息
                put("price", JSONObject().apply {
                    put("total", product.price.total)
                    put("value", product.price.value)
                    put("market", product.price.market)
                    put("lineprice", product.price.lineprice)

                    // 备份价格详情列表
                    val priceAfterArray = org.json.JSONArray()
                    product.price.priceafterlist.forEach { priceAfter ->
                        priceAfterArray.put(JSONObject().apply {
                            put("text", priceAfter.text)
                            put("type", priceAfter.type)
                        })
                    }
                    put("priceafterlist", priceAfterArray)
                })

                // 备份规格信息
                put("spec", JSONObject().apply {
                    put("desc", product.spec.desc)
                })

                // 备份商品标签
                val productTagsArray = org.json.JSONArray()
                product.producttags.forEach { tag ->
                    productTagsArray.put(tag)
                }
                put("producttags", productTagsArray)

                // 备份标签列表
                val tagListArray = org.json.JSONArray()
                product.taglist.forEach { tag ->
                    tagListArray.put(JSONObject().apply {
                        put("type", tag.type)
                        put("text", tag.text)
                        put("sort", tag.sort)
                    })
                }
                put("taglist", tagListArray)
            }
            productsArray.put(productJson)
        }
        backupJson.put("products", productsArray)

        // 备份操作信息
        val actionInfosArray = org.json.JSONArray()
        order.actioninfos.forEach { actionInfo ->
            actionInfosArray.put(JSONObject().apply {
                put("highlight", actionInfo.highlight)
                put("actionname", actionInfo.actionname)
                put("actiontype", actionInfo.actiontype)
                put("actionurl", actionInfo.actionurl)

                // 备份额外信息
                val extraJson = JSONObject()
                actionInfo.extra.forEach { (key, value) ->
                    extraJson.put(key, value)
                }
                put("extra", extraJson)

                // 备份原因列表
                val reasonsArray = org.json.JSONArray()
                actionInfo.reasons.forEach { reason ->
                    reasonsArray.put(reason)
                }
                put("reasons", reasonsArray)
            })
        }
        backupJson.put("actioninfos", actionInfosArray)

        // 备份预期时间信息（如果存在）
        order.texpecttime?.let { expectTime ->
            backupJson.put("expectTime", JSONObject().apply {
                put("date", expectTime.date)

                val timeSlotsArray = org.json.JSONArray()
                expectTime.timeslots.forEach { timeSlot ->
                    timeSlotsArray.put(JSONObject().apply {
                        put("from", timeSlot.from)
                        put("to", timeSlot.to)
                        put("slottype", timeSlot.slottype)
                        put("immediatedescription", timeSlot.immediatedescription)
                    })
                }
                put("timeslots", timeSlotsArray)
            })
        }

        return backupJson
    }
}

/**
 * 删除订单统计信息数据类
 */
data class DeletedOrderStatistics(
    val totalCount: Int = 0,
    val todayCount: Int = 0,
    val thisWeekCount: Int = 0,
    val thisMonthCount: Int = 0
)
