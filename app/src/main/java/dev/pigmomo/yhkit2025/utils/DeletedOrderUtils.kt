package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.api.model.order.Order
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.model.DeletedOrderEntity
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 删除订单记录工具类
 * 提供便捷的删除订单记录查询和统计功能
 */
object DeletedOrderUtils {
    
    private const val TAG = "DeletedOrderUtils"
    
    /**
     * 获取所有删除订单记录
     */
    fun getAllDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getAllDeletedOrders()
    }
    
    /**
     * 根据账号UID获取删除订单记录
     */
    fun getDeletedOrdersByUid(context: Context, uid: String): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getDeletedOrdersByUid(uid)
    }
    
    /**
     * 根据手机号获取删除订单记录
     */
    fun getDeletedOrdersByPhoneNumber(context: Context, phoneNumber: String): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getDeletedOrdersByPhoneNumber(phoneNumber)
    }
    
    /**
     * 获取今日删除的订单记录
     */
    fun getTodayDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfDay = calendar.timeInMillis
        
        calendar.add(Calendar.DAY_OF_MONTH, 1)
        val startOfNextDay = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfDay, startOfNextDay)
    }
    
    /**
     * 获取本周删除的订单记录
     */
    fun getThisWeekDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        
        // 设置为本周第一天（周一）
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfWeek = calendar.timeInMillis
        
        // 设置为下周第一天
        calendar.add(Calendar.WEEK_OF_YEAR, 1)
        val startOfNextWeek = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfWeek, startOfNextWeek)
    }
    
    /**
     * 获取本月删除的订单记录
     */
    fun getThisMonthDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        
        // 设置为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfMonth = calendar.timeInMillis
        
        // 设置为下月第一天
        calendar.add(Calendar.MONTH, 1)
        val startOfNextMonth = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfMonth, startOfNextMonth)
    }
    
    /**
     * 检查订单是否已被删除
     */
    suspend fun isOrderDeleted(context: Context, orderId: String): Boolean {
        return try {
            val database = AppDatabase.getDatabase(context)
            database.deletedOrderDao().isOrderDeleted(orderId)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check if order is deleted: ${e.message}")
            false
        }
    }
    
    /**
     * 获取删除订单统计信息
     */
    suspend fun getDeletedOrderStatistics(context: Context): DeletedOrderStatistics {
        return try {
            val database = AppDatabase.getDatabase(context)
            val dao = database.deletedOrderDao()
            
            val totalCount = dao.getDeletedOrderCount()
            
            // 获取今日删除数量
            val calendar = Calendar.getInstance()
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val startOfNextDay = calendar.timeInMillis
            val todayCount = dao.getDeletedOrderCountByDateRange(startOfDay, startOfNextDay)
            
            // 获取本周删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfWeek = calendar.timeInMillis
            calendar.add(Calendar.WEEK_OF_YEAR, 1)
            val startOfNextWeek = calendar.timeInMillis
            val thisWeekCount = dao.getDeletedOrderCountByDateRange(startOfWeek, startOfNextWeek)
            
            // 获取本月删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_MONTH, 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfMonth = calendar.timeInMillis
            calendar.add(Calendar.MONTH, 1)
            val startOfNextMonth = calendar.timeInMillis
            val thisMonthCount = dao.getDeletedOrderCountByDateRange(startOfMonth, startOfNextMonth)
            
            DeletedOrderStatistics(
                totalCount = totalCount,
                todayCount = todayCount,
                thisWeekCount = thisWeekCount,
                thisMonthCount = thisMonthCount
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get deleted order statistics: ${e.message}")
            DeletedOrderStatistics()
        }
    }
    
    /**
     * 获取指定账号的删除订单统计信息
     */
    suspend fun getDeletedOrderStatisticsByUid(context: Context, uid: String): DeletedOrderStatistics {
        return try {
            val database = AppDatabase.getDatabase(context)
            val dao = database.deletedOrderDao()
            
            val totalCount = dao.getDeletedOrderCountByUid(uid)
            
            // 获取今日删除数量
            val calendar = Calendar.getInstance()
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val startOfNextDay = calendar.timeInMillis
            val todayRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfDay, startOfNextDay).first()
            val todayCount = todayRecords.size
            
            // 获取本周删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfWeek = calendar.timeInMillis
            calendar.add(Calendar.WEEK_OF_YEAR, 1)
            val startOfNextWeek = calendar.timeInMillis
            val thisWeekRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfWeek, startOfNextWeek).first()
            val thisWeekCount = thisWeekRecords.size
            
            // 获取本月删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_MONTH, 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfMonth = calendar.timeInMillis
            calendar.add(Calendar.MONTH, 1)
            val startOfNextMonth = calendar.timeInMillis
            val thisMonthRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfMonth, startOfNextMonth).first()
            val thisMonthCount = thisMonthRecords.size
            
            DeletedOrderStatistics(
                totalCount = totalCount,
                todayCount = todayCount,
                thisWeekCount = thisWeekCount,
                thisMonthCount = thisMonthCount
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get deleted order statistics by uid: ${e.message}")
            DeletedOrderStatistics()
        }
    }
    
    /**
     * 格式化删除时间
     */
    fun formatDeleteTime(timestamp: Long): String {
        return try {
            val date = Date(timestamp)
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            formatter.format(date)
        } catch (e: Exception) {
            "时间格式错误"
        }
    }
    
    /**
     * 清理指定时间之前的删除记录（用于数据清理）
     */
    suspend fun cleanupOldRecords(context: Context, daysToKeep: Int = 30): Int {
        return try {
            val database = AppDatabase.getDatabase(context)
            val calendar = Calendar.getInstance()
            calendar.add(Calendar.DAY_OF_MONTH, -daysToKeep)
            val cutoffTime = calendar.timeInMillis

            database.deletedOrderDao().deleteDeletedOrdersBeforeTimestamp(cutoffTime)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup old records: ${e.message}")
            0
        }
    }

    /**
     * 备份已删除的订单记录到文件
     * @param context 上下文
     * @param deletedOrderEntity 删除订单记录实体
     * @return 备份文件路径，失败时返回null
     */
    fun backupDeletedOrderRecord(
        context: Context,
        deletedOrderEntity: DeletedOrderEntity
    ): String? {
        return try {
            // 创建备份数据的JSON对象
            val backupData = createDeletedOrderRecordJson(deletedOrderEntity)

            // 生成文件名（包含时间戳和订单ID）
            val timestamp = DateUtils.formatDateTime(System.currentTimeMillis(), "yyyyMMdd_HHmmss")
            val fileName = "deleted_order_record_${deletedOrderEntity.orderId}_${timestamp}.json"

            // 保存到指定的备份目录
            val filePath = FileUtils.saveTextToPublicDownloads(
                context,
                fileName,
                backupData.toString(2), // 格式化JSON，缩进2个空格
                "BackupDeleteOrders"
            ) ?: FileUtils.saveTextToDownloads(
                context,
                fileName,
                backupData.toString(2),
                "BackupDeleteOrders"
            )

            if (filePath != null) {
                Log.d(TAG, "Deleted order record backup saved to: $filePath")
            } else {
                Log.e(TAG, "Failed to save deleted order record backup for order: ${deletedOrderEntity.orderId}")
            }

            filePath
        } catch (e: Exception) {
            Log.e(TAG, "Error backing up deleted order record: ${e.message}", e)
            null
        }
    }

    /**
     * 创建删除订单记录的JSON数据
     * @param deletedOrderEntity 删除订单记录实体
     * @return JSON对象
     */
    private fun createDeletedOrderRecordJson(
        deletedOrderEntity: DeletedOrderEntity
    ): JSONObject {
        val backupJson = JSONObject()

        // 备份基本信息
        backupJson.put("backupInfo", JSONObject().apply {
            put("backupTimestamp", System.currentTimeMillis())
            put("backupDate", SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date()))
            put("backupType", "DeletedOrderEntity")
        })

        // 备份删除订单记录数据
        backupJson.put("deletedOrderRecord", JSONObject().apply {
            put("id", deletedOrderEntity.id)
            put("orderId", deletedOrderEntity.orderId)
            put("uid", deletedOrderEntity.uid)
            put("phoneNumber", deletedOrderEntity.phoneNumber)
            put("orderTitle", deletedOrderEntity.orderTitle)
            put("orderStatus", deletedOrderEntity.orderStatus)
            put("deleteTimestamp", deletedOrderEntity.deleteTimestamp)
            put("deleteDate", deletedOrderEntity.deleteDate)
            put("extraNote", deletedOrderEntity.extraNote)
        })

        return backupJson
    }

    /**
     * 批量备份删除订单记录到文件
     * @param context 上下文
     * @return 备份文件路径，失败时返回null
     */
    suspend fun backupAllDeletedOrderRecords(context: Context): String? {
        return try {
            val database = AppDatabase.getDatabase(context)
            val allRecords = database.deletedOrderDao().getAllDeletedOrders().first()

            if (allRecords.isEmpty()) {
                Log.d(TAG, "No deleted order records to backup")
                return null
            }

            // 创建批量备份数据的JSON对象
            val backupJson = JSONObject()

            // 备份基本信息
            backupJson.put("backupInfo", JSONObject().apply {
                put("backupTimestamp", System.currentTimeMillis())
                put("backupDate", SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date()))
                put("backupType", "AllDeletedOrderRecords")
                put("totalRecords", allRecords.size)
            })

            // 备份所有删除订单记录
            val recordsArray = org.json.JSONArray()
            allRecords.forEach { record ->
                recordsArray.put(JSONObject().apply {
                    put("id", record.id)
                    put("orderId", record.orderId)
                    put("uid", record.uid)
                    put("phoneNumber", record.phoneNumber)
                    put("orderTitle", record.orderTitle)
                    put("orderStatus", record.orderStatus)
                    put("deleteTimestamp", record.deleteTimestamp)
                    put("deleteDate", record.deleteDate)
                    put("extraNote", record.extraNote)
                })
            }
            backupJson.put("deletedOrderRecords", recordsArray)

            // 生成文件名（包含时间戳）
            val timestamp = DateUtils.formatDateTime(System.currentTimeMillis(), "yyyyMMdd_HHmmss")
            val fileName = "all_deleted_order_records_${timestamp}.json"

            // 保存到指定的备份目录
            val filePath = FileUtils.saveTextToPublicDownloads(
                context,
                fileName,
                backupJson.toString(2), // 格式化JSON，缩进2个空格
                "BackupDeleteOrders"
            ) ?: FileUtils.saveTextToDownloads(
                context,
                fileName,
                backupJson.toString(2),
                "BackupDeleteOrders"
            )

            if (filePath != null) {
                Log.d(TAG, "All deleted order records backup saved to: $filePath")
            } else {
                Log.e(TAG, "Failed to save all deleted order records backup")
            }

            filePath
        } catch (e: Exception) {
            Log.e(TAG, "Error backing up all deleted order records: ${e.message}", e)
            null
        }
    }
}

/**
 * 删除订单统计信息数据类
 */
data class DeletedOrderStatistics(
    val totalCount: Int = 0,
    val todayCount: Int = 0,
    val thisWeekCount: Int = 0,
    val thisMonthCount: Int = 0
)
