package dev.pigmomo.yhkit2025.viewmodel

import android.annotation.SuppressLint
import android.app.Application
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import android.widget.Toast
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.content.ContextCompat.startActivity
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.model.cart.CartModelTypes
import dev.pigmomo.yhkit2025.api.model.cart.CartModelWrapper
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.api.model.user.AddressItem
import dev.pigmomo.yhkit2025.api.model.cart.CartItem
import dev.pigmomo.yhkit2025.api.model.user.AddressInfo
import dev.pigmomo.yhkit2025.api.model.card.CardItem
import dev.pigmomo.yhkit2025.api.model.user.CityInfo
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceCoupon
import dev.pigmomo.yhkit2025.api.model.coupon.CouponCategory
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceData
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceResponse
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceRedPacket
import dev.pigmomo.yhkit2025.api.model.user.ShopInfo
import dev.pigmomo.yhkit2025.api.utils.ResponseParserUtils
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.data.repository.TokenRepository
import dev.pigmomo.yhkit2025.utils.ClipboardUtils
import dev.pigmomo.yhkit2025.utils.TokenParserUtils
import dev.pigmomo.yhkit2025.utils.HttpProxyUtils
import dev.pigmomo.yhkit2025.utils.ProgressManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.UUID
import dev.pigmomo.yhkit2025.api.model.boostcoupon.BoostCouponRecord
import dev.pigmomo.yhkit2025.api.model.cart.CartData
import dev.pigmomo.yhkit2025.api.model.coupon.CouponListCoupon
import dev.pigmomo.yhkit2025.api.model.order.Order
import dev.pigmomo.yhkit2025.api.model.invitatinv2.InvitationReward
import dev.pigmomo.yhkit2025.api.model.invitatinv2.SuccessInvite
import dev.pigmomo.yhkit2025.api.model.credit.CreditData
import dev.pigmomo.yhkit2025.api.model.credit.CreditDetail
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailData
import dev.pigmomo.yhkit2025.api.model.user.HomepageResponse
import dev.pigmomo.yhkit2025.api.model.order.InvoiceCanApplyOrder
import dev.pigmomo.yhkit2025.api.model.order.AfterSalesOrder
import androidx.core.net.toUri
import androidx.lifecycle.ViewModel
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.model.handluck.HandLuckReward
import dev.pigmomo.yhkit2025.api.model.order.OrderDeliveryInfoData
import dev.pigmomo.yhkit2025.api.utils.AliPayConvertUtils
import dev.pigmomo.yhkit2025.api.model.invitatinv2.InvitationV2ActivityInfoBaseInfo
import dev.pigmomo.yhkit2025.api.model.invitatinv2.InvitationV2ActivityInfoData
import dev.pigmomo.yhkit2025.api.utils.AddressUtils
import dev.pigmomo.yhkit2025.api.utils.SignUtils
import dev.pigmomo.yhkit2025.utils.PhoneLocationUtils
import dev.pigmomo.yhkit2025.utils.ProcessRecorder
import dev.pigmomo.yhkit2025.data.model.LogEntity
import dev.pigmomo.yhkit2025.data.repository.LogRepository
import dev.pigmomo.yhkit2025.data.dao.DeletedOrderDao
import dev.pigmomo.yhkit2025.data.model.DeletedOrderEntity
import java.text.SimpleDateFormat
import java.util.Locale
import dev.pigmomo.yhkit2025.hooks.config.XPrefsWriter
import dev.pigmomo.yhkit2025.ui.dialog.MultiFilterOptions
import dev.pigmomo.yhkit2025.utils.DateUtils
import dev.pigmomo.yhkit2025.utils.DeletedOrderUtils
import dev.pigmomo.yhkit2025.utils.EncryptionUtils
import kotlinx.coroutines.CoroutineScope
import dev.pigmomo.yhkit2025.utils.FailedTokenIndexRecordUtils
import dev.pigmomo.yhkit2025.utils.OrderViewModelCacheHelper
import kotlinx.coroutines.flow.Flow

class OrderViewModel(
    private val tokenRepository: TokenRepository,
    application: Application
) : AndroidViewModel(application) {

    // 导出账号相关状态
    private val _accountExporting = mutableStateOf(false)
    val accountExporting: State<Boolean> = _accountExporting

    // 导入账号相关状态
    private val _accountImporting = mutableStateOf(false)
    val accountImporting: State<Boolean> = _accountImporting

    // 服务实例
    internal var requestService: RequestService? = null

    // 服务类型
    private val _serviceType = mutableStateOf("app")
    val serviceType: State<String> = _serviceType

    // 代理相关状态
    private val _showProxySelectDialog = mutableStateOf(false)
    val showProxySelectDialog: State<Boolean> = _showProxySelectDialog

    // 选中的代理
    private val _selectedProxy = mutableStateOf("")
    val selectedProxy: State<String> = _selectedProxy

    // 是否启用代理
    private val _enableProxy = mutableStateOf(false)
    val enableProxy: State<Boolean> = _enableProxy

    // 应用上下文
    private val appContext = getApplication<Application>().applicationContext

    // 添加clipboardManager变量
    private val clipboardManager: ClipboardManager by lazy {
        getApplication<Application>().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    }

    // 订单Token列表
    private val _orderTokens = MutableStateFlow<List<OrderTokenEntity>>(emptyList())
    val orderTokens: StateFlow<List<OrderTokenEntity>> = _orderTokens.asStateFlow()

    // 当前已选中账号\Token
    private val _currentSelectedAccount = mutableStateOf<OrderTokenEntity?>(null)
    val currentSelectedAccount: State<OrderTokenEntity?> = _currentSelectedAccount

    // 当前选中的地区
    private val _selectedDistrict = mutableStateOf("")
    val selectedDistrict: State<String> = _selectedDistrict

    // 当前选中的地址项
    private val _selectedAddressItem = mutableStateOf<AddressItem?>(null)
    val selectedAddressItem: State<AddressItem?> = _selectedAddressItem

    // 当前选中的购物车
    private val _currentCartItem = mutableStateOf<CartItem?>(null)
    val currentCartItem: State<CartItem?> = _currentCartItem

    // 账号\Token点击和长按状态管理
    private val _clickedTokenUid = mutableStateOf("")
    val clickedTokenUid: State<String> = _clickedTokenUid
    private val _longClickedTokenUid = mutableStateOf("")
    val longClickedTokenUid: State<String> = _longClickedTokenUid
    private val _currentOperationOrderTokenIndex = mutableStateOf(-1)
    val currentOperationOrderTokenIndex: State<Int> = _currentOperationOrderTokenIndex

    // Token信息加载状态
    private val _isLoadingTokenInfo = mutableStateOf(false)
    val isLoadingTokenInfo: State<Boolean> = _isLoadingTokenInfo

    // 详情对话框状态
    private val _isTokenDetailDialogVisible = mutableStateOf(false)
    val isTokenDetailDialogVisible: State<Boolean> = _isTokenDetailDialogVisible

    // 自动下单状态
    private val _autoOrderPlace = mutableStateOf(false)
    val autoOrderPlace: State<Boolean> = _autoOrderPlace

    // 添加锁对象，保护自动下单状态的修改
    private val autoOrderPlaceLock = Any()

    // 搜索框显示状态
    private val _showSearchFrame = mutableStateOf(false)
    val showSearchFrame: State<Boolean> = _showSearchFrame

    // 批量账号操作对话框显示状态
    private val _showOrderTokensOperateDialog = mutableStateOf(false)
    val showOrderTokensOperateDialog: State<Boolean> = _showOrderTokensOperateDialog

    // 批量活动操作对话框显示状态
    private val _showActivitiesOperateDialog = mutableStateOf(false)
    val showActivitiesOperateDialog: State<Boolean> = _showActivitiesOperateDialog

    // 是否显示地址下拉菜单
    private val _showAddressMenu = mutableStateOf(false)
    val showAddressMenu: State<Boolean> = _showAddressMenu

    // 是否显示购物车对话框
    private val _showCartDialog = mutableStateOf(false)
    val showCartDialog: State<Boolean> = _showCartDialog

    // 账号展示文本
    private val _accountDisplayText = mutableStateOf("账号未设定")
    val accountDisplayText: State<String> = _accountDisplayText

    // 地址展示文本
    private val _addressDisplayText = mutableStateOf("地址未同步")
    val addressDisplayText: State<String> = _addressDisplayText

    // 购物车展示文本
    private val _cartDisplayText = mutableStateOf("购物车未同步")
    val cartDisplayText: State<String> = _cartDisplayText

    // 店铺展示文本
    private val _shopDisplayText = mutableStateOf("店铺未初始化")
    val shopDisplayText: State<String> = _shopDisplayText

    // 搜索关键词
    private val _searchKeyword = mutableStateOf("")
    val searchKeyword: State<String> = _searchKeyword

    // 地址列表
    private val _addressList = mutableStateOf<List<AddressItem>>(emptyList())
    val addressList: State<List<AddressItem>> = _addressList

    // 店铺列表
    private val _shopList = mutableStateOf<List<ShopInfo>>(emptyList())
    val shopList: State<List<ShopInfo>> = _shopList

    // 当前选中的店铺
    private val _currentShopItem = mutableStateOf<ShopInfo?>(null)
    val currentShopItem: State<ShopInfo?> = _currentShopItem

    // 购物车列表
    private val _cartList = mutableStateOf<List<CartItem>>(emptyList())
    val cartList: State<List<CartItem>> = _cartList

    // 购物车是否有选中商品状态
    private val _hasSelectedCartItems = mutableStateOf(false)
    val hasSelectedCartItems: State<Boolean> = _hasSelectedCartItems

    // 商品配置字符串
    private val _productConfigStr = mutableStateOf("")
    val productConfigStr: State<String> = _productConfigStr

    // 添加商品弹窗状态
    private val _showAddProductDialog = mutableStateOf(false)
    val showAddProductDialog: State<Boolean> = _showAddProductDialog

    // 添加地址对话框状态
    private val _showAddAddressDialog = mutableStateOf(false)
    val showAddAddressDialog: State<Boolean> = _showAddAddressDialog

    // 提交OrderPlace弹窗状态
    private val _showOrderPlaceDialog = mutableStateOf(false)
    val showOrderPlaceDialog: State<Boolean> = _showOrderPlaceDialog

    // 显示账户对话框状态
    private val _showAccountDialog = mutableStateOf(false)
    val showAccountDialog: State<Boolean> = _showAccountDialog

    // 清空订单Token列表弹窗
    private val _showClearAllTokensConfirmDialog = mutableStateOf(false)
    val showClearAllTokensConfirmDialog: State<Boolean> = _showClearAllTokensConfirmDialog

    // 清空地址弹窗状态
    private val _showClearAddressConfirmDialog = mutableStateOf(false)
    val showClearAddressConfirmDialog: State<Boolean> = _showClearAddressConfirmDialog

    // 清空购物车弹窗状态
    private val _showClearCartConfirmDialog = mutableStateOf(false)
    val showClearCartConfirmDialog: State<Boolean> = _showClearCartConfirmDialog

    // 删除地址弹窗状态
    private val _showDeleteAddressConfirmDialog = mutableStateOf(false)
    val showDeleteAddressConfirmDialog: State<Boolean> = _showDeleteAddressConfirmDialog

    // 删除购物车商品弹窗状态
    private val _showDeleteCartItemConfirmDialog = mutableStateOf(false)
    val showDeleteCartItemConfirmDialog: State<Boolean> = _showDeleteCartItemConfirmDialog

    // 将删除的地址
    private val _addressToDelete = mutableStateOf<AddressItem?>(null)
    val addressToDelete: State<AddressItem?> = _addressToDelete

    // 将删除的购物车商品
    private val _cartItemProductToDelete = mutableStateOf<Product?>(null)
    val cartItemProductToDelete: State<Product?> = _cartItemProductToDelete

    // 添加地址位置相关配置
    private val _addAddressStr = mutableStateOf("")
    val addAddressStr: State<String> = _addAddressStr

    // 添加地址位置
    private val _addAddressLocationStr = mutableStateOf("")
    val addAddressLocationStr: State<String> = _addAddressLocationStr

    // 随机地址正则
    private val _randomDetailsRegex = mutableStateOf("XX单元XX栋XX-X号")
    val randomDetailsRegex: State<String> = _randomDetailsRegex

    // 添加地址的店铺
    private val _addAddressShopItem = mutableStateOf<ShopInfo?>(null)
    val addAddressShopItem: State<ShopInfo?> = _addAddressShopItem

    // 是否固定地址
    private val _fastenAddress = mutableStateOf(false)
    val fastenAddress: State<Boolean> = _fastenAddress

    // 是否固定经纬度
    private val _fastenLatLng = mutableStateOf(false)
    val fastenLatLng: State<Boolean> = _fastenLatLng

    // 订单备注
    private val _orderComment = mutableStateOf("")
    val orderComment: State<String> = _orderComment

    // 是否生成支付URL
    private val _genPayUrl = mutableStateOf(false)
    val genPayUrl: State<Boolean> = _genPayUrl

    // 提交OrderPlace响应
    private val _orderPlaceResponse = mutableStateOf<OrderPlaceResponse?>(null)
    val orderPlaceResponse: State<OrderPlaceResponse?> = _orderPlaceResponse

    // 选中的优惠券
    private val _selectedOrderPlaceCoupon = mutableStateOf<OrderPlaceCoupon?>(null)
    val selectedOrderPlaceCoupon: State<OrderPlaceCoupon?> = _selectedOrderPlaceCoupon

    // 选中的红包
    private val _selectedOrderPlaceRedPacket = mutableStateOf<OrderPlaceRedPacket?>(null)
    val selectedOrderPlaceRedPacket: State<OrderPlaceRedPacket?> = _selectedOrderPlaceRedPacket

    // 余额支付选项
    private val _balancePayOption = mutableIntStateOf(0)
    val balancePayOption: State<Int> = _balancePayOption

    // 积分支付选项
    private val _pointPayOption = mutableIntStateOf(0)
    val pointPayOption: State<Int> = _pointPayOption

    // 自提选项
    private val _isPickSelf = mutableIntStateOf(0)
    val isPickSelf: State<Int> = _isPickSelf

    // 期望送达时间
    private val _expectTime = mutableStateOf("")
    val expectTime: State<String> = _expectTime

    // 商品总金额
    private val _productsTotalAmount = mutableStateOf("")
    val productsTotalAmount: State<String> = _productsTotalAmount

    // 总支付金额
    private val _totalPaymentNew = mutableStateOf("")
    val totalPaymentNew: State<String> = _totalPaymentNew

    // 永辉卡列表
    private val _cardList = mutableStateOf<List<CardItem>>(emptyList())
    val cardList: State<List<CardItem>> = _cardList

    // 永辉卡余额
    private val _cardBalance = mutableStateOf("0")
    val cardBalance: State<String> = _cardBalance

    // 优惠券相关状态
    private val _couponList = mutableStateOf<List<CouponListCoupon>>(emptyList())
    val couponList: State<List<CouponListCoupon>> = _couponList

    // 不可用优惠券列表
    private val _unavailableCouponList = mutableStateOf<List<CouponListCoupon>>(emptyList())
    val unavailableCouponList: State<List<CouponListCoupon>> = _unavailableCouponList

    // 助力券相关状态
    private val _boostCouponList = mutableStateOf<List<BoostCouponRecord>>(emptyList())
    val boostCouponList: State<List<BoostCouponRecord>> = _boostCouponList

    // 订单列表相关状态
    private val _orderList = mutableStateOf<List<Order>>(emptyList())
    val orderList: State<List<Order>> = _orderList

    // 是否还有下一页
    private val _hasNextPage = mutableStateOf(false)
    val hasNextPage: State<Boolean> = _hasNextPage

    // 最后一页订单ID
    private val _lastOrderId = mutableStateOf("")
    val lastOrderId: State<String> = _lastOrderId

    // 订单列表状态统计
    private val _orderListInfo = mutableStateOf("")
    val orderListInfo: State<String> = _orderListInfo

    // 修改订单配送信息弹窗状态
    private val _showOrderDeliveryInfoDialog = mutableStateOf(false)
    val showOrderDeliveryInfoDialog: State<Boolean> = _showOrderDeliveryInfoDialog

    // 修改订单配送信息
    private val _orderDeliveryInfo = mutableStateOf<OrderDeliveryInfoData?>(null)
    val orderDeliveryInfo: State<OrderDeliveryInfoData?> = _orderDeliveryInfo

    // 修改订单可用地址
    private val _availableAddress = mutableStateOf<List<AddressItem>>(emptyList())
    val availableAddress: State<List<AddressItem>> = _availableAddress

    // 邀请相关状态
    private val _invitationRewards = mutableStateOf<List<InvitationReward>>(emptyList())
    val invitationRewards: State<List<InvitationReward>> = _invitationRewards

    // 成功邀请列表
    private val _successInvites = mutableStateOf<List<SuccessInvite>>(emptyList())
    val successInvites: State<List<SuccessInvite>> = _successInvites

    // 积分相关状态
    private val _creditData = mutableStateOf<CreditData?>(null)
    val creditData: State<CreditData?> = _creditData

    // 积分详情列表
    private val _creditDetails = mutableStateOf<List<CreditDetail>>(emptyList())
    val creditDetails: State<List<CreditDetail>> = _creditDetails

    // 积分数量
    private val _creditCount = mutableIntStateOf(0)
    val creditCount: State<Int> = _creditCount

    // 组队瓜分团队代码
    private val _pointTeamCode = mutableStateOf("")
    val pointTeamCode: State<String> = _pointTeamCode

    // 是否显示加入组队瓜分对话框
    private val _showJoinPointTeamDialog = mutableStateOf(false)
    val showJoinPointTeamDialog: State<Boolean> = _showJoinPointTeamDialog

    // 登录状态
    private val _needLogin = mutableStateOf(false)
    val needLogin: State<Boolean> = _needLogin

    // 账户类型状态
    private val _accountType = mutableStateOf<String>("")
    val accountType: State<String> = _accountType

    // 检查是否能参加活动
    private val _checkActivityStr = mutableStateOf("")
    val checkActivityStr: State<String> = _checkActivityStr

    // 检查是否能购买永辉卡
    private val _checkCardBuyStr = mutableStateOf("")
    val checkCardBuyStr: State<String> = _checkCardBuyStr

    // 绑定永辉卡弹窗状态
    private val _showBindCardDialog = mutableStateOf(false)
    val showBindCardDialog: State<Boolean> = _showBindCardDialog

    // 添加赠送永辉卡确认弹窗状态
    private val _showSendCardDialog = mutableStateOf(false)
    val showSendCardDialog: State<Boolean> = _showSendCardDialog

    // 当前选中的要赠送的卡
    private val _selectedCardToSend = mutableStateOf<CardItem?>(null)
    val selectedCardToSend: State<CardItem?> = _selectedCardToSend

    // 添加取消赠送永辉卡弹窗状态
    private val _showCancelSendCardDialog = mutableStateOf(false)
    val showCancelSendCardDialog: State<Boolean> = _showCancelSendCardDialog

    // 当前选中的要取消赠送的卡
    private val _selectedCardToCancel = mutableStateOf<CardItem?>(null)
    val selectedCardToCancel: State<CardItem?> = _selectedCardToCancel

    // 新人优惠券弹窗状态
    private val _showNewPersonCouponDialog = mutableStateOf(false)
    val showNewPersonCouponDialog: State<Boolean> = _showNewPersonCouponDialog

    // 新人优惠券店铺参数
    private val _newPersonCouponShopParams = mutableStateOf("")
    val newPersonCouponShopParams: State<String> = _newPersonCouponShopParams

    // 新人券参数快速设置对话框状态
    private val _showNewPersonCouponShopParamsFastSetDialog = mutableStateOf(false)
    val showNewPersonCouponShopParamsFastSetDialog: State<Boolean> =
        _showNewPersonCouponShopParamsFastSetDialog

    // 显示活动规则对话框
    private val _showCommonActivityRuleDialog = mutableStateOf(false)
    val showCommonActivityRuleDialog: State<Boolean> = _showCommonActivityRuleDialog

    // 新人券活动规则
    private val _commonActivityRule = mutableStateOf("")
    val commonActivityRule: State<String> = _commonActivityRule

    // 领取优惠券弹窗状态
    private val _showKindCouponDialog = mutableStateOf(false)
    val showKindCouponDialog: State<Boolean> = _showKindCouponDialog

    // 领取优惠券参数
    private val _couponPromotionCodeStr = mutableStateOf("")
    val couponPromotionCodeStr: State<String> = _couponPromotionCodeStr

    // 拼手气弹窗状态
    private val _showHandLuckDialog = mutableStateOf(false)
    val showHandLuckDialog: State<Boolean> = _showHandLuckDialog

    // 拼手气事件ID
    private val _handLuckEventIdStr = mutableStateOf("")
    val handLuckEventIdStr: State<String> = _handLuckEventIdStr

    // 拼手气奖励列表
    private val _handLuckRewardList = mutableStateOf<List<HandLuckReward>>(emptyList())
    val handLuckRewardList: State<List<HandLuckReward>> = _handLuckRewardList

    // 拼手气奖励列表弹窗状态
    private val _showHandLuckRewardListDialog = mutableStateOf(false)
    val showHandLuckRewardListDialog: State<Boolean> = _showHandLuckRewardListDialog

    // 助力券配置弹窗状态
    private val _showBoostCouponConfigDialog = mutableStateOf(false)
    val showBoostCouponConfigDialog: State<Boolean> = _showBoostCouponConfigDialog

    // 助力券数据
    private val _boostCouponHelpData = mutableStateOf<List<String>>(emptyList())
    val boostCouponHelpData: State<List<String>> = _boostCouponHelpData

    // 抢券配置弹窗状态
    private val _showCouponGrabbingConfigDialog = mutableStateOf(false)
    val showCouponGrabbingConfigDialog: State<Boolean> = _showCouponGrabbingConfigDialog

    // 订单查询弹窗状态
    private val _showOrderSearchDialog = mutableStateOf(false)
    val showOrderSearchDialog: State<Boolean> = _showOrderSearchDialog

    // 订单ID
    private val _recordOrderId = mutableStateOf("")
    val recordOrderId: State<String> = _recordOrderId

    // 消费记录查询订单弹窗状态
    private val _showCardRecordOrderListDialog = mutableStateOf(false)
    val showCardRecordOrderListDialog: State<Boolean> = _showCardRecordOrderListDialog

    // 消费记录订单列表
    private val _cardRecordOrderList = mutableStateOf<List<String>>(emptyList())
    val cardRecordOrderList: State<List<String>> = _cardRecordOrderList

    // 消费记录订单列表分页相关状态
    private val _cardRecordOrderListHasNextPage = mutableStateOf(false)
    val cardRecordOrderListHasNextPage: State<Boolean> = _cardRecordOrderListHasNextPage

    // 消费记录订单列表当前页
    private val _cardRecordOrderListCurrentPage = mutableStateOf("1")
    val cardRecordOrderListCurrentPage: State<String> = _cardRecordOrderListCurrentPage

    // 发票可申请订单列表弹窗状态
    private val _showInvoiceCanApplyOrderListDialog = mutableStateOf(false)
    val showInvoiceCanApplyOrderListDialog: State<Boolean> = _showInvoiceCanApplyOrderListDialog

    // 发票可申请订单列表
    private val _invoiceCanApplyOrderList = mutableStateOf<List<InvoiceCanApplyOrder>>(emptyList())
    val invoiceCanApplyOrderList: State<List<InvoiceCanApplyOrder>> = _invoiceCanApplyOrderList

    // 发票可申请订单列表当前页
    private val _invoiceCanApplyOrderPage = mutableStateOf(0)
    val invoiceCanApplyOrderPage: State<Int> = _invoiceCanApplyOrderPage

    // 发票可申请订单列表页数
    private val _invoiceCanApplyOrderPageCount = mutableStateOf(0)
    val invoiceCanApplyOrderPageCount: State<Int> = _invoiceCanApplyOrderPageCount

    // 售后订单列表弹窗状态
    private val _showAfterSalesListDialog = mutableStateOf(false)
    val showAfterSalesListDialog: State<Boolean> = _showAfterSalesListDialog

    // 售后订单列表
    private val _afterSalesOrderList = mutableStateOf<List<AfterSalesOrder>>(emptyList())
    val afterSalesOrderList: State<List<AfterSalesOrder>> = _afterSalesOrderList

    // 售后订单列表当前页
    private val _afterSalesOrderPage = mutableStateOf(0)
    val afterSalesOrderPage: State<Int> = _afterSalesOrderPage

    // 售后订单列表页数
    private val _afterSalesOrderPageCount = mutableStateOf(0)
    val afterSalesOrderPageCount: State<Int> = _afterSalesOrderPageCount

    // 订单详情数据
    private val _orderDetailData = mutableStateOf<OrderDetailData?>(null)
    val orderDetailData: State<OrderDetailData?> = _orderDetailData

    // 送达照片相关状态
    private val _showDeliveryPhotosDialog = mutableStateOf(false)
    val showDeliveryPhotosDialog: State<Boolean> = _showDeliveryPhotosDialog

    // 送达照片
    private val _deliveryPhotos = mutableStateOf<List<String>>(emptyList())
    val deliveryPhotos: State<List<String>> = _deliveryPhotos

    // 优惠券详情弹窗状态
    private val _showCouponDetailDialog = mutableStateOf(false)
    val showCouponDetailDialog: State<Boolean> = _showCouponDetailDialog

    // 当前选中的优惠券
    private val _selectedCoupon = mutableStateOf<CouponListCoupon?>(null)
    val selectedCoupon: State<CouponListCoupon?> = _selectedCoupon

    // 助力券详情弹窗状态
    private val _showBoostCouponDetailDialog = mutableStateOf(false)
    val showBoostCouponDetailDialog: State<Boolean> = _showBoostCouponDetailDialog

    // 当前选中的助力券
    private val _selectedBoostCoupon = mutableStateOf<BoostCouponRecord?>(null)
    val selectedBoostCoupon: State<BoostCouponRecord?> = _selectedBoostCoupon

    // 清空订单弹窗状态
    private val _showClearOrderConfirmDialog = mutableStateOf(false)
    val showClearOrderConfirmDialog: State<Boolean> = _showClearOrderConfirmDialog

    // 绑定邀请码弹窗状态
    private val _showBindInvitationCodeDialog = mutableStateOf(false)
    val showBindInvitationCodeDialog: State<Boolean> = _showBindInvitationCodeDialog

    // 绑定邀请码
    private val _bindInvitationCode = mutableStateOf("")
    val bindInvitationCode: State<String> = _bindInvitationCode

    // 自动下单弹窗状态
    private val _showOrderAutoPlaceDialog = mutableStateOf(false)
    val showOrderAutoPlaceDialog: State<Boolean> = _showOrderAutoPlaceDialog

    // 多线程对话框状态
    private val _showMultiThreadDialog = mutableStateOf(false)
    val showMultiThreadDialog: State<Boolean> = _showMultiThreadDialog

    // 多线程范围数组
    private val _multiThreadRangeList = mutableStateOf<List<Int>>(emptyList())
    val multiThreadRangeList: State<List<Int>> = _multiThreadRangeList

    // 多线程范围数组字符串
    private val _multiThreadRangeListStr = mutableStateOf("")
    val multiThreadRangeListStr: State<String> = _multiThreadRangeListStr

    // 多线程启用状态
    private val _multiThreadEnabled = mutableStateOf(false)
    val multiThreadEnabled: State<Boolean> = _multiThreadEnabled

    // 操作间隔设置对话框状态
    private val _showOperationIntervalDialog = mutableStateOf(false)
    val showOperationIntervalDialog: State<Boolean> = _showOperationIntervalDialog

    // 操作间隔时间（毫秒）
    private val _operationIntervalMs = mutableStateOf(0)
    val operationIntervalMs: State<Int> = _operationIntervalMs

    // 是否正在操作Token
    private val _isOrderTokensOperating = mutableStateOf(false)
    val isOrderTokensOperating: State<Boolean> = _isOrderTokensOperating

    // 是否正在操作活动
    private val _isActivitiesOperating = mutableStateOf(false)
    val isActivitiesOperating: State<Boolean> = _isActivitiesOperating

    // 是否正在抢券
    private val _isCouponGrabbing = mutableStateOf(false)
    val isCouponGrabbing: State<Boolean> = _isCouponGrabbing

    // 抢券倒计时
    private val _couponGrabbingRemainingTime = mutableStateOf(0L)
    val couponGrabbingRemainingTime: State<Long> = _couponGrabbingRemainingTime

    // 当前IP
    private val _realProxyIp = mutableStateOf("")
    val realProxyIp: State<String> = _realProxyIp

    // 手机号归属地
    private val _phoneLocation = mutableStateOf("")
    val phoneLocation: State<String> = _phoneLocation

    // 进程记录器
    private var processRecorder: ProcessRecorder? = null

    // 日志仓库
    private lateinit var logRepository: LogRepository

    // 删除订单DAO
    private lateinit var deletedOrderDao: DeletedOrderDao

    // 当前账号的日志列表
    private val _currentAccountLogs = MutableStateFlow<List<LogEntity>>(emptyList())
    val currentAccountLogs: StateFlow<List<LogEntity>> = _currentAccountLogs.asStateFlow()

    // 删除订单记录相关状态
    private val _showDeletedOrderRecordsDialog = mutableStateOf(false)
    val showDeletedOrderRecordsDialog: State<Boolean> = _showDeletedOrderRecordsDialog

    private val _deletedOrderRecords = MutableStateFlow<List<DeletedOrderEntity>>(emptyList())
    val deletedOrderRecords: StateFlow<List<DeletedOrderEntity>> =
        _deletedOrderRecords.asStateFlow()

    // 日志加载状态
    private val _isLoadingLogs = mutableStateOf(false)
    val isLoadingLogs: State<Boolean> = _isLoadingLogs

    // 用于撤回功能的最后移动的令牌
    private val _lastMovedToken = mutableStateOf<OrderTokenEntity?>(null)
    val lastMovedToken: State<OrderTokenEntity?> = _lastMovedToken

    // 用于控制滚动到指定令牌的状态
    private val _scrollToTokenUid = mutableStateOf("")
    val scrollToTokenUid: State<String> = _scrollToTokenUid

    // 日志分页参数
    private val _logPageOffset = mutableIntStateOf(0)
    val logPageOffset: State<Int> = _logPageOffset

    private val _logPageSize = mutableIntStateOf(10)
    val logPageSize: State<Int> = _logPageSize

    // 添加账号对话框状态
    private val _showAddAccountDialog = mutableStateOf(false)
    val showAddAccountDialog: State<Boolean> = _showAddAccountDialog

    // 添加账号对话框内容
    private val _addAccountContent = mutableStateOf("")
    val addAccountContent: State<String> = _addAccountContent

    // 检查剪贴板
    var checkClipboardOnResume by mutableStateOf(false)

    // 活动类型
    private val _activityGetType = mutableStateOf("1")
    val activityGetType: State<String> = _activityGetType

    // 导出订单弹窗状态
    private val _showOrderExportDialog = mutableStateOf(false)
    val showOrderExportDialog: State<Boolean> = _showOrderExportDialog

    // 导出账号对话框可见性
    private val _isExportAccountDialogVisible = mutableStateOf(false)
    val isExportAccountDialogVisible: State<Boolean> = _isExportAccountDialogVisible

    // 导出筛选选项
    private val _exportFilterOptions = mutableStateOf(MultiFilterOptions())

    // 导出账号对话框是否添加参数到备注
    private val _addParamsToNote = mutableStateOf(false)
    val addParamsToNote: State<Boolean> = _addParamsToNote

    // 抢券配置
    private val _couponGrabbingConfig = mutableStateOf("")
    val couponGrabbingConfig: State<String> = _couponGrabbingConfig

    init {
        loadOrderTokens()
    }

    private fun loadOrderTokens() {
        viewModelScope.launch {
            tokenRepository.getAllOrderTokens().collect { tokens ->
                _orderTokens.value = tokens
            }
        }
    }

    fun updateTokenLogin(token: OrderTokenEntity) {
        viewModelScope.launch {
            tokenRepository.updateOrderToken(token)
        }
    }

    fun setClickedTokenUid(uid: String) {
        _clickedTokenUid.value = uid
    }

    fun setLongClickedTokenUid(uid: String) {
        _longClickedTokenUid.value = uid
    }

    fun setTokenDetailDialogVisible(visible: Boolean) {
        _isTokenDetailDialogVisible.value = visible
    }

    fun setServiceType(serviceType: String) {
        _serviceType.value = serviceType
    }

    fun setAutoOrderPlace(auto: Boolean) {
        synchronized(autoOrderPlaceLock) {
            val oldValue = _autoOrderPlace.value
            _autoOrderPlace.value = auto
            Log.d("OrderViewModel", "Auto order place status changed: $oldValue -> $auto")
        }
    }

    fun setLoadingTokenInfo(loading: Boolean) {
        _isLoadingTokenInfo.value = loading
    }

    fun setShowSearchFrame(show: Boolean) {
        _showSearchFrame.value = show
    }

    fun setShowOrderTokensOperateDialog(show: Boolean) {
        _showOrderTokensOperateDialog.value = show
    }

    fun setShowActivitiesOperateDialog(show: Boolean) {
        _showActivitiesOperateDialog.value = show
    }

    fun setActivityGetType(activityGetType: String) {
        _activityGetType.value = activityGetType
    }

    fun setSelectedDistrict(district: String) {
        _selectedDistrict.value = district
    }

    // 恢复账号缓存数据相关状态
    fun setRequestService(tempRequestService: RequestService?) {
        requestService = tempRequestService
    }

    fun setAddressList(addressList: List<AddressItem>) {
        _addressList.value = addressList
    }

    fun setShopList(shopList: List<ShopInfo>) {
        _shopList.value = shopList
    }

    fun setSelectedAddressItem(addressItem: AddressItem?) {
        _selectedAddressItem.value = addressItem
    }

    // 设置购物车相关数据
    fun setCartList(cartList: List<CartItem>) {
        _cartList.value = cartList
    }

    // 设置账号类型和登录状态
    fun setAccountType(accountType: String) {
        _accountType.value = accountType
    }

    fun setNeedLogin(needLogin: Boolean) {
        _needLogin.value = needLogin
    }

    // 设置账号检测相关状态
    fun setCheckActivityStr(checkActivityStr: String) {
        _checkActivityStr.value = checkActivityStr
    }

    fun setCheckCardBuyStr(checkCardBuyStr: String) {
        _checkCardBuyStr.value = checkCardBuyStr
    }

    // 设置永辉卡相关状态
    fun setCardList(cardList: List<CardItem>) {
        _cardList.value = cardList
    }

    fun setCardBalance(cardBalance: String) {
        _cardBalance.value = cardBalance
    }

    // 设置优惠券相关状态
    fun setCouponList(couponList: List<CouponListCoupon>) {
        _couponList.value = couponList
    }

    fun setUnavailableCouponList(unavailableCouponList: List<CouponListCoupon>) {
        _unavailableCouponList.value = unavailableCouponList
    }

    // 设置助力券相关状态
    fun setBoostCouponList(boostCouponList: List<BoostCouponRecord>) {
        _boostCouponList.value = boostCouponList
    }

    // 设置订单列表相关状态
    fun setOrderList(orderList: List<Order>) {
        _orderList.value = orderList
    }

    fun setHasNextPage(hasNextPage: Boolean) {
        _hasNextPage.value = hasNextPage
    }

    fun setLastOrderId(lastOrderId: String) {
        _lastOrderId.value = lastOrderId
    }

    fun setOrderListInfo(orderListInfo: String) {
        _orderListInfo.value = orderListInfo
    }

    // 设置邀请相关状态
    fun setInvitationRewards(invitationRewards: List<InvitationReward>) {
        _invitationRewards.value = invitationRewards
    }

    fun setSuccessInvites(successInvites: List<SuccessInvite>) {
        _successInvites.value = successInvites
    }

    // 设置积分相关状态
    fun setCreditData(creditData: CreditData) {
        _creditData.value = creditData
    }

    fun setCreditDetails(creditDetails: List<CreditDetail>) {
        _creditDetails.value = creditDetails
    }

    fun setCreditCount(creditCount: Int) {
        _creditCount.intValue = creditCount
    }

    // 设置IP/归属地相关状态
    fun setRealProxyIp(realProxyIp: String) {
        _realProxyIp.value = realProxyIp
    }

    fun setPhoneLocation(phoneLocation: String) {
        _phoneLocation.value = phoneLocation
    }

    /**
     * 设置是否显示地址下拉菜单
     * @param show 是否显示
     */
    fun setShowAddressMenu(show: Boolean) {
        _showAddressMenu.value = show
    }

    /**
     * 设置是否显示购物车对话框
     * @param show 是否显示
     */
    fun setShowCartDialog(show: Boolean) {
        _showCartDialog.value = show
    }

    fun setSearchKeyword(keyword: String) {
        _searchKeyword.value = keyword
    }

    /**
     * 设置账号展示文本
     */
    fun setAccountDisplayText(text: String) {
        _accountDisplayText.value = text
    }

    /**
     * 设置当前选中的购物车
     */
    fun setCurrentCartItem(item: CartItem?) {
        _currentCartItem.value = item

        // 更新购物车选中状态
        updateSelectedCartItemsState()
    }

    /**
     * 设置地址展示文本
     */
    fun setAddressDisplayText(text: String) {
        _addressDisplayText.value = text
    }

    /**
     * 设置购物车展示文本
     */
    fun setCartDisplayText(text: String) {
        _cartDisplayText.value = text
    }

    /**
     * 设置商品配置字符串
     */
    fun setProductConfigStr(text: String) {
        _productConfigStr.value = text
    }

    /**
     * 设置店铺展示文本
     */
    fun setShopDisplayText(text: String) {
        _shopDisplayText.value = text
    }

    /**
     * 设置当前已选中账号
     */
    fun setCurrentSelectedAccount(account: OrderTokenEntity?) {
        _currentSelectedAccount.value = account
    }

    fun setShowOrderPlaceDialog(show: Boolean) {
        _showOrderPlaceDialog.value = show
    }

    fun setOrderPlaceResponse(response: OrderPlaceResponse?) {
        _orderPlaceResponse.value = response
    }

    fun setSelectedCoupon(orderPlaceCoupon: OrderPlaceCoupon?) {
        _selectedOrderPlaceCoupon.value = orderPlaceCoupon
    }

    fun setSelectedOrderPlaceRedPacket(orderPlaceRedPacket: OrderPlaceRedPacket?) {
        _selectedOrderPlaceRedPacket.value = orderPlaceRedPacket
    }

    fun setBalancePayOption(option: Int) {
        _balancePayOption.intValue = option
    }

    fun setPointPayOption(option: Int) {
        _pointPayOption.intValue = option
    }

    fun setIsPickSelf(isPickSelf: Int) {
        _isPickSelf.intValue = isPickSelf
    }

    /**
     * 设置期望送达时间
     */
    fun setExpectTime(time: String) {
        _expectTime.value = time
    }

    /**
     * 设置导出订单弹窗状态
     */
    fun setShowOrderExportDialog(show: Boolean) {
        _showOrderExportDialog.value = show
    }

    /**
     * 设置导出账号对话框可见性
     * @param visible 是否可见
     */
    fun setExportAccountDialogVisible(visible: Boolean) {
        _isExportAccountDialogVisible.value = visible
    }

    /**
     * 处理导出账号确认
     * @param filterOptions 多选筛选条件
     */
    fun onExportAccountsConfirm(filterOptions: MultiFilterOptions, addParamsToNote: Boolean) {
        _exportFilterOptions.value = filterOptions
        _addParamsToNote.value = addParamsToNote
    }

    /**
     * 导出账号到指定URI
     * @param uri 导出文件的URI
     */
    fun exportAccountsToUri(uri: Uri) {
        _accountExporting.value = true
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val tokenDao = AppDatabase.getDatabase(appContext).orderTokenDao()
                    val allTokens = tokenDao.getAllOrderTokens().first()

                    if (allTokens.isEmpty()) {
                        showToast("数据库数据为空")
                        return@withContext
                    }

                    // 根据筛选条件过滤账号
                    val filteredTokens = if (_exportFilterOptions.value == MultiFilterOptions()) {
                        // 如果没有选择任何筛选条件，则导出所有账号
                        allTokens
                    } else {
                        allTokens.filter { token ->
                            // 新人/非新人筛选
                            val passesNewcomerFilter =
                                when (_exportFilterOptions.value.isNewcomer) {
                                    true -> token.isNew
                                    false -> !token.isNew
                                    null -> true // 不筛选新人状态
                                }

                            // 积分筛选
                            val passesCreditFilter = when {
                                _exportFilterOptions.value.creditLessThan2500 == true -> token.credit < 2500
                                _exportFilterOptions.value.creditGreaterThanEqual2500 == true -> token.credit >= 2500
                                else -> true // 不筛选积分状态
                            }

                            // 账号状态筛选
                            val passesAccountFilter =
                                when (_exportFilterOptions.value.accountValid) {
                                    true -> token.isLogin // 账号有效
                                    false -> !token.isLogin // 账号无效
                                    null -> true // 不筛选账号状态
                                }

                            // 永辉卡限制状态筛选
                            val passesYhCardFilter =
                                when (_exportFilterOptions.value.yhCardLimitedStatus) {
                                    true -> token.yhCardLimited // 永辉卡受限
                                    false -> !token.yhCardLimited // 永辉卡非限制
                                    null -> true // 不筛选永辉卡状态
                                }

                            // 活动限制状态筛选
                            val passesActivityFilter =
                                when (_exportFilterOptions.value.activityLimitedStatus) {
                                    true -> token.activityLimited // 活动受限
                                    false -> !token.activityLimited // 活动非限制
                                    null -> true // 不筛选活动状态
                                }

                            // 余额状态筛选
                            val passesCardBalanceFilter =
                                when (_exportFilterOptions.value.cardBalanceStatus) {
                                    true -> token.cardBalance == 0f // 余额=0
                                    false -> token.cardBalance > 0 // 余额>0
                                    null -> true // 不筛选余额状态
                                }

                            // 所有条件都必须满足
                            passesNewcomerFilter &&
                                    passesAccountFilter &&
                                    passesYhCardFilter &&
                                    passesActivityFilter &&
                                    passesCreditFilter &&
                                    passesCardBalanceFilter
                        }
                    }

                    if (filteredTokens.isEmpty()) {
                        showToast("没有符合条件的账号")
                        return@withContext
                    }

                    val addParamsToNote = addParamsToNote.value

                    val exportContent = filteredTokens.joinToString("\n") { token ->
                        val extraNote = if (addParamsToNote) {
                            val tokenExtraNote = token.extraNote
                            val needAddNote = if (token.isNew) {
                                "新人特权"
                            } else {
                                "非新人"
                            } + if (token.yhCardLimited) {
                                "/黑"
                            } else {
                                "/白"
                            } + if (token.activityLimited) {
                                "/黑"
                            } else {
                                "/白"
                            } + "/" + token.credit.toString() + "/" + if (token.cardBalance == 0f) "0" else token.cardBalance.toString()

                            // 检查原备注中是否已包含要添加的标记
                            if (tokenExtraNote.isNotEmpty()) {
                                // 检查是否已包含相同的标记
                                if (tokenExtraNote.contains("新人特权") || tokenExtraNote.contains("非新人")) {
                                    // 原备注中已包含标记，使用正则表达式替换原有标记
                                    // 匹配形如"新人特权/白/白/123/456.78" 或 "非新人/黑/黑/123/456.78" 的模式
                                    // 积分是整数，余额可能有小数
                                    val pattern =
                                        "(新人特权|非新人)(/[黑白]){2}/\\d+/\\d+(\\.\\d+)?".toRegex()
                                    if (pattern.containsMatchIn(tokenExtraNote)) {
                                        // 查找并替换匹配的部分，保留其他部分
                                        val matchResult = pattern.find(tokenExtraNote)
                                        if (matchResult != null) {
                                            // 替换匹配的部分
                                            val beforeMatch =
                                                tokenExtraNote.substring(0, matchResult.range.first)
                                            val afterMatch =
                                                tokenExtraNote.substring(matchResult.range.last + 1)
                                            beforeMatch + needAddNote + afterMatch
                                        } else {
                                            // 理论上不应该到这里，因为containsMatchIn已经确认有匹配
                                            "$tokenExtraNote $needAddNote"
                                        }
                                    } else {
                                        // 包含关键词但不匹配完整模式，添加新标记
                                        "$tokenExtraNote $needAddNote"
                                    }
                                } else {
                                    // 原备注中不包含标记，直接添加
                                    "$tokenExtraNote $needAddNote"
                                }
                            } else {
                                // 原备注为空，直接使用新标记
                                needAddNote
                            }
                        } else {
                            token.extraNote
                        }

                        "${token.phoneNumber}----${token.uid}----${token.userKey}-601933-${token.accessToken}----${token.refreshToken}----${token.appParam},${token.expiresIn}----${token.updateDate} $extraNote"
                    }

                    // 写入到用户选择的URI
                    appContext.contentResolver.openOutputStream(uri)?.use { outputStream ->
                        outputStream.write(exportContent.toByteArray())
                    }

                    showToast("成功导出${filteredTokens.size}个账号")
                }
            } catch (e: Exception) {
                e.printStackTrace()
                showToast("导出失败: ${e.message}")
            } finally {
                _accountExporting.value = false
            }
        }
    }

    /**
     * 保存订单下单配置
     * 保存用户设置的下单选项，包括优惠券、红包、支付方式、自提选项和期望送达时间
     *
     * @param selectedOrderPlaceCoupon 选择的优惠券
     * @param selectedOrderPlaceRedPacket 选择的红包
     * @param balancePayOption 余额支付选项
     * @param pointPayOption 积分支付选项
     * @param pickSelf 是否自提
     * @param expectTime 期望送达时间
     */
    fun saveOrderPlaceConfig(
        selectedOrderPlaceCoupon: OrderPlaceCoupon?,
        selectedOrderPlaceRedPacket: OrderPlaceRedPacket?,
        balancePayOption: Int,
        pointPayOption: Int,
        pickSelf: Int,
        expectTime: String,
        productsTotalAmount: String,
        totalPaymentNew: String
    ) {
        viewModelScope.launch {
            try {
                // 更新各状态值
                _selectedOrderPlaceCoupon.value = selectedOrderPlaceCoupon
                _selectedOrderPlaceRedPacket.value = selectedOrderPlaceRedPacket
                _balancePayOption.intValue = balancePayOption
                _pointPayOption.intValue = pointPayOption
                _isPickSelf.intValue = pickSelf
                _expectTime.value = expectTime
                _productsTotalAmount.value = productsTotalAmount
                _totalPaymentNew.value = totalPaymentNew

                // 记录配置信息
                Log.d(
                    "OrderViewModel", "Order place config saved: " +
                            "coupon=$selectedOrderPlaceCoupon, " +
                            "redPacket=$selectedOrderPlaceRedPacket, " +
                            "balancePay=$balancePayOption, " +
                            "pointPay=$pointPayOption, " +
                            "pickSelf=$pickSelf, " +
                            "expectTime=$expectTime, " +
                            "productsTotalAmount=$productsTotalAmount, " +
                            "totalPaymentNew=$totalPaymentNew"
                )

                showToast("下单配置已保存")
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Error saving order place config", e)
                showToast("保存下单配置失败: ${e.message}")
            }
        }
    }

    /**
     * 设置自动下单弹窗状态
     */
    fun setShowOrderAutoPlaceDialog(show: Boolean) {
        _showOrderAutoPlaceDialog.value = show
    }

    /**
     * 设置多线程对话框状态
     */
    fun setShowMultiThreadDialog(
        show: Boolean,
        type: FailedTokenIndexRecordUtils.OperationType? = null
    ) {
        _showMultiThreadDialog.value = show

        if (show && type != null) {
            val failedTokenIndexes =
                FailedTokenIndexRecordUtils.getFailedTokenIndexesRangeString(type)
            Log.d("OrderViewModel", "failedTokenIndexes: $failedTokenIndexes")
            setMultiThreadRangeListStr(failedTokenIndexes)
        }
    }

    /**
     * 设置多线程范围列表
     */
    fun setMultiThreadRangeList(rangeArray: List<Int>) {
        _multiThreadRangeList.value = rangeArray
    }

    /**
     * 设置多线程范围列表字符串
     */
    fun setMultiThreadRangeListStr(rangeArrayStr: String) {
        _multiThreadRangeListStr.value = rangeArrayStr
    }

    /**
     * 设置多线程启用状态
     */
    fun setMultiThreadEnabled(enabled: Boolean) {
        _multiThreadEnabled.value = enabled
    }

    /**
     * 设置操作间隔设置对话框状态
     */
    fun setShowOperationIntervalDialog(show: Boolean) {
        _showOperationIntervalDialog.value = show
    }

    /**
     * 设置操作间隔时间
     */
    fun setOperationIntervalMs(intervalMs: Int) {
        _operationIntervalMs.value = intervalMs
    }

    /**
     * 设置是否正在操作Token
     */
    fun setIsOrderTokensOperating(isOperating: Boolean) {
        _isOrderTokensOperating.value = isOperating
    }

    /**
     * 设置是否正在操作活动
     */
    fun setIsActivitiesOperating(isOperating: Boolean) {
        _isActivitiesOperating.value = isOperating
    }

    /**
     * 设置是否正在抢券
     */
    fun setIsCouponGrabbing(isGrabbing: Boolean) {
        _isCouponGrabbing.value = isGrabbing
    }

    /**
     * 设置抢券倒计时
     */
    fun setCouponGrabbingRemainingTime(remainingTime: Long) {
        _couponGrabbingRemainingTime.value = remainingTime
    }

    /**
     * 通用方法：复制文本到剪贴板
     * @param label 标签
     * @param text 要复制的文本
     */
    private fun copyToClipboard(label: String, text: String) {
        val clip = ClipData.newPlainText(label, text)
        clipboardManager.setPrimaryClip(clip)
        showToast("已复制${label}到剪贴板")
    }

    /**
     * 复制Token信息到剪贴板
     */
    fun copyTokenToClipboard(orderToken: OrderTokenEntity) {
        ClipboardUtils.copyToken(clipboardManager, orderToken)
        showToast("已复制到剪贴板")
    }

    /**
     * 复制手机号到剪贴板
     */
    fun copyPhoneNumberToClipboard(orderToken: OrderTokenEntity) {
        copyToClipboard("手机号", orderToken.phoneNumber)
    }

    /**
     * 复制备注到剪贴板
     */
    fun copyExtraNoteToClipboard(orderToken: OrderTokenEntity) {
        copyToClipboard("备注", orderToken.extraNote)
    }

    /**
     * 加载Token并获取相关信息（地址、店铺、购物车等）
     * @param token 要加载的订单Token实体
     */
    fun loadOrderTokenInfo(token: OrderTokenEntity, isLoadCache: Boolean = true) {
        viewModelScope.launch {
            // 检查服务
            if (serviceType.value == "app" && !SignUtils.checkServer()) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)

            // 在开始加载新Token前，保存当前的账号的缓存数据以便下次加载时快速载入
            // 包括_addressList、_shopList、_cartList、_selectedAddressItem、_accountType、_needLogin、_checkActivityStr、_checkCardBuyStr、_cardList、_cardBalance、_couponList、_unavailableCouponList、_boostCouponList、_orderList、_hasNextPage、_lastOrderId、_orderListInfo、_invitationRewards、_successInvites、_creditData、_creditDetails、_creditCount、_realProxyIp、_phoneLocation
            val isSaveCache = saveCurrentTokenCacheData()
            if (!isSaveCache) {
                Log.d("OrderViewModel", "${token.phoneNumber} 保存账号缓存数据错误")
            }

            // 设置当前选中的账号
            setClickedTokenUid(token.uid)
            setCurrentSelectedAccount(token)

            // 初始化进程记录器
            processRecorder = ProcessRecorder(
                tokenUid = token.uid,
                phoneNumber = token.phoneNumber,
                tag = "OrderView",
                logLevelDefault = "INFO",
                context = appContext
            )

            // 尝试从缓存中加载数据
            if (isLoadCache) {
                val loadedFromCache =
                    OrderViewModelCacheHelper.loadTokenCacheData(
                        this@OrderViewModel,
                        token.uid,
                        token.phoneNumber
                    )
                if (loadedFromCache) {
                    setLoadingTokenInfo(false)
                    processRecorder?.recordProcess("从缓存加载账号成功")
                    Log.d("OrderViewModel", "${token.phoneNumber} 从缓存加载账号成功")
                    showToast("从缓存切换账号成功")
                    return@launch
                }
            }

            // 在开始加载新Token前，重置相关UI状态和数据
            _addressList.value = emptyList()
            _shopList.value = emptyList()
            _cartList.value = emptyList()
            // 如果固定地址，则不重置地址
            if (!fastenAddress.value) {
                _selectedAddressItem.value = null
            }
            setAccountDisplayText("账号未设定")
            // 如果固定地址，则不重置地址
            if (!fastenAddress.value) {
                setAddressDisplayText("地址未同步")
            }
            setShopDisplayText("店铺未初始化")
            setCartDisplayText("购物车未同步")

            // 重置账户类型
            _accountType.value = ""
            _needLogin.value = false

            // 重置账号检测相关状态
            _checkActivityStr.value = ""
            _checkCardBuyStr.value = ""

            // 重置永辉卡相关状态
            _cardList.value = emptyList()
            _cardBalance.value = "0"

            // 重置优惠券相关状态
            _couponList.value = emptyList()
            _unavailableCouponList.value = emptyList()

            // 重置助力券相关状态
            _boostCouponList.value = emptyList()

            // 重置订单列表相关状态
            _orderList.value = emptyList()
            _hasNextPage.value = false
            _lastOrderId.value = ""
            _orderListInfo.value = ""

            // 重置邀请相关状态
            _invitationRewards.value = emptyList()
            _successInvites.value = emptyList()

            // 重置积分相关状态
            _creditData.value = null
            _creditDetails.value = emptyList()
            _creditCount.intValue = 0

            // 重置IP/归属地相关状态
            _realProxyIp.value = ""
            _phoneLocation.value = ""

            try {
                // 停止当前正在进行的请求
                requestService?.stop()

                // 初始化服务
                val currentService = RequestService.create(token, serviceType.value)
                requestService = currentService
                processRecorder?.recordProcess("RequestService(${serviceType.value}) 服务初始化完成")

                setAccountDisplayText(token.phoneNumber) // 服务创建成功后，更新为实际账号信息

                // 获取手机号归属地（异步执行，不阻塞主流程）
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        val phoneLocation =
                            PhoneLocationUtils.getPhoneLocationFromFly63(token.phoneNumber)
                        _phoneLocation.value = phoneLocation
                        processRecorder?.recordProcess("手机号归属地: ${token.phoneNumber} $phoneLocation")
                    } catch (e: Exception) {
                        Log.e("OrderViewModel", "Failed to get phone location: ${e.message}")
                    }
                }

                // 启用代理
                if (_selectedProxy.value.isNotEmpty()) {
                    processRecorder?.recordProcess("代理配置: ${_selectedProxy.value}")
                    val proxyConfig =
                        HttpProxyUtils.getProxyIp(selectProxyConfig = _selectedProxy.value)
                    if (proxyConfig.proxyInfo.first != null && proxyConfig.proxyInfo.second != null) {
                        // 获取代理实际IP和端口
                        val actualProxyInfo = HttpProxyUtils.getProxyIpInfo(
                            proxyConfig.proxyInfo,
                            proxyConfig.proxyAccount
                        )
                        _realProxyIp.value = actualProxyInfo.ip
                        if (_realProxyIp.value.isEmpty()) {
                            Log.e("OrderViewModel", "Failed to get actual proxy info")
                            processRecorder?.recordProcess(
                                "当前IP获取错误，停止流程(loadToken)",
                                "ERROR"
                            )
                            return@launch
                        } else {
                            processRecorder?.recordProcess("当前IP: ${_realProxyIp.value}")
                        }

                        val success = currentService.setupProxy(proxyConfig)
                        if (success) {
                            Log.d(
                                "OrderViewModel",
                                "Proxy setup successful: ${proxyConfig.proxyInfo.first}:${proxyConfig.proxyInfo.second}"
                            )
                            processRecorder?.recordProcess("代理设置: ${proxyConfig.proxyInfo.first}:${proxyConfig.proxyInfo.second}")
                        } else {
                            Log.e("OrderViewModel", "Failed to setup proxy")
                            processRecorder?.recordProcess(
                                "代理配置错误: Failed to setup proxy",
                                "ERROR"
                            )
                            //showToast("代理异常")
                            return@launch
                        }
                    } else {
                        Log.e("OrderViewModel", "Failed to get proxy info")
                        processRecorder?.recordProcess(
                            "代理配置错误: Failed to get proxy info",
                            "ERROR"
                        )
                        //showToast("代理异常")
                        return@launch
                    }
                } else {
                    val actualProxyInfo = HttpProxyUtils.getProxyIpInfo()
                    _realProxyIp.value = actualProxyInfo.ip
                    if (_realProxyIp.value.isEmpty()) {
                        Log.e("OrderViewModel", "Failed to get actual proxy info")
                        processRecorder?.recordProcess(
                            "未获取到当前IP，停止流程(loadToken)",
                            "ERROR"
                        )
                        return@launch
                    } else {
                        processRecorder?.recordProcess("当前IP: ${_realProxyIp.value}")
                    }
                }

                // 检查账号类型和登录状态
                val needLogin = checkAccountType { setAccountDisplayText(it) }

                // 只有当不需要登录时，才加载地址及后续数据
                if (!needLogin) {
                    // 获取地址及后续数据
                    if (!fastenAddress.value) {
                        fetchAddressesAndThenDependentData(currentService)
                    } else {
                        processRecorder?.recordProcess("使用固定地址，跳过地址列表获取")
                        fetchDependentDataWithExistingAddress(currentService)
                    }

                    // 同步账号的余额，订单、积分
                    if (selectedAddressItem.value != null) {
                        syncAccountSomeData()
                    }
                } else {
                    // 如果需要登录，显示提示
                    processRecorder?.recordProcess("账号已失效，停止流程(loadToken)", "ERROR")
                    //setAccountDisplayText("账号未登录")
                    setAddressDisplayText("地址未同步")
                    setShopDisplayText("店铺未初始化")
                    setCartDisplayText("购物车未同步")
                }

            } catch (e: Exception) {
                Log.e("OrderViewModel", "Error loading token", e)
                processRecorder?.recordProcess("加载账号错误: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
                processRecorder?.recordProcess("账号加载流程已结束")
            }
        }
    }

    /**
     * 使用已有的selectedAddressItem获取shop和cart数据，不重新获取地址列表
     * 此函数假设_selectedAddressItem已经设置，直接使用它获取shop和cart数据
     * @param currentService RequestService 实例
     */
    private suspend fun fetchDependentDataWithExistingAddress(currentService: RequestService) {
        withContext(Dispatchers.IO) {
            val currentSelectedAddress = _selectedAddressItem.value
            processRecorder?.recordProcess("已固定地址，使用固定地址: ${currentSelectedAddress?.address ?: "未知"}")

            // 清空选中地址的id，避免使用非该账号的地址id
            _selectedAddressItem.value?.id = ""

            if (currentSelectedAddress != null) {
                // 使用已有的地址来加载店铺、XYH参数和购物车
                fetchShopCartAndXyhForAddress(currentSelectedAddress, currentService)
            } else {
                processRecorder?.recordProcess(
                    "已固定地址为空，停止流程(fetchDependentDataWithExistingAddress)",
                    "ERROR"
                )
            }
        }
    }

    /**
     * 获取地址列表，如果成功，则获取与第一个地址相关的店铺、XYH参数和购物车数据。
     * @param currentService RequestService 实例
     */
    private suspend fun fetchAddressesAndThenDependentData(currentService: RequestService) {
        withContext(Dispatchers.IO) {
            val addressResult = currentService.address.getAllAddress()
            withContext(Dispatchers.Main) {
                processAddressResult(addressResult) // 处理地址结果并更新UI, 这也会更新 _selectedAddressItem
            }

            val currentSelectedAddress = _selectedAddressItem.value // 使用更新后的选中地址

            if (currentSelectedAddress != null) {
                // 使用获取到的选中的地址来加载店铺、XYH参数和购物车
                fetchShopCartAndXyhForAddress(currentSelectedAddress, currentService)
            } else {
                // 地址列表为空或处理错误，清空店铺和购物车相关显示
                // processAddressResult 应该已经处理了地址相关的UI
                processRecorder?.recordProcess(
                    "地址列表为空或处理错误，停止流程(fetchAddressesAndThenDependentData)",
                    "ERROR"
                )
                withContext(Dispatchers.Main) {
                    clearShopAndCartDisplayOnAddressEmpty() // 确保依赖数据也被清理
                }
            }
        }
    }

    /**
     * 根据给定的地址项获取店铺信息、XYH业务参数和购物车数据。
     * @param addressItem 地址项
     * @param currentService RequestService 实例
     */
    private suspend fun fetchShopCartAndXyhForAddress(
        addressItem: AddressItem,
        currentService: RequestService
    ) {
        withContext(Dispatchers.IO) {
            // 获取店铺信息
            val shopResult = currentService.shop.getFbShopLbs(addressItem)
            withContext(Dispatchers.Main) {
                processShopResult(shopResult) // 处理店铺结果并更新UI
            }

            // 解析店铺信息以获取后续请求所需的参数
            val shopResponse = shopResult.let {
                if (it is RequestResult.Success) ResponseParserUtils.parseShopResponse(it.data) else null
            }

            if (shopResponse != null && shopResponse.code == 0) {
                val addressInfo = shopResponse.data?.address?.firstOrNull()
                val cityInfo = shopResponse.data?.city
                val shopInfoList = shopResponse.data?.seller

                if (addressInfo != null && cityInfo != null && !shopInfoList.isNullOrEmpty()) {
                    val sellerId = shopInfoList[0].sellerid.toString()
                    val shopId = shopInfoList[0].shopid
                    processRecorder?.recordProcess("店铺信息: ${shopInfoList[0].shopname}")

                    // 获取XYH业务参数
                    when (val xyhFetchResult = getXyhBizParamsAndUpdateService(
                        currentService = currentService,
                        addressInfo = addressInfo,
                        cityInfo = cityInfo,
                        sellerId = sellerId,
                        shopId = shopId
                    )) {
                        is XyhFetchResult.Success -> {
                            // XYH参数获取成功，继续获取购物车
                            val cartResult = currentService.cart.getAllCart(addressItem.id)
                            withContext(Dispatchers.Main) {
                                processCartResult(cartResult)
                            }
                        }

                        is XyhFetchResult.Error -> {
                            // XYH参数获取失败
                            withContext(Dispatchers.Main) {
                                handleXyhFetchError(xyhFetchResult.errorMessage)
                            }
                        }
                    }
                } else {
                    // 店铺响应数据不完整
                    withContext(Dispatchers.Main) {
                        Log.w("OrderViewModel", "Shop response data incomplete for XYH params.")
                        setCartDisplayText("店铺数据不完整")
                        processRecorder?.recordProcess(
                            "店铺数据不完整，停止流程(fetchShopCartAndXyhForAddress)",
                            "ERROR"
                        )
                        _cartList.value = emptyList()
                        showToast("店铺数据不完整")
                    }
                }
            } else {
                // 获取店铺信息错误或店铺响应为空 (shopResponse.code != 0 or shopResponse is null)
                // processShopResult 已经处理了店铺相关的UI更新和Toast (setShopDisplayText, _shopList.value)
                // 此处确保购物车状态也被正确更新
                withContext(Dispatchers.Main) {
                    // 不需要重复Log，processShopResult已记录
                    setCartDisplayText("店铺信息获取错误")
                    processRecorder?.recordProcess(
                        "店铺信息获取错误，停止流程(fetchShopCartAndXyhForAddress)",
                        "ERROR"
                    )
                    _cartList.value = emptyList()
                }
            }
        }
    }

    /**
     * 获取XYH业务参数并更新RequestService实例。
     * @param currentService RequestService实例
     * @param addressInfo 地址详细信息
     * @param cityInfo 城市信息
     * @param sellerId 卖家ID
     * @param shopId 店铺ID
     * @return XyhFetchResult 包含成功或失败信息
     */
    private suspend fun getXyhBizParamsAndUpdateService(
        currentService: RequestService,
        addressInfo: AddressInfo,
        cityInfo: CityInfo,
        sellerId: String,
        shopId: String
    ): XyhFetchResult {
        try {
            val addressId = addressInfo.id
            val lat = addressInfo.location?.lat ?: ""
            val lng = addressInfo.location?.lng ?: ""
            val cityId = cityInfo.id
            val district = addressInfo.address.district

            val result = currentService.getXyhBizParams(
                lat = lat,
                lng = lng,
                cityid = cityId,
                district = district,
                sellerid = sellerId,
                shopid = shopId,
                addressId = addressId,
                serviceType = currentService.serviceType
            )

            return when (result) {
                is RequestResult.Success -> {
                    val xyhBizParams = result.data
                    currentService.setXyhBizParams(xyhBizParams)
                    val webXyhBizParams =
                        AddressUtils.matchAndAssignParamsByXYHBizParamsCommon(xyhBizParams)
                    currentService.setWebXyhBizParams(webXyhBizParams)
                    Log.d("OrderViewModel", "XYH fetched successfully.")
                    processRecorder?.recordProcess("XYH业务参数：$xyhBizParams")
                    XyhFetchResult.Success
                }

                is RequestResult.Error -> {
                    val errorMsg =
                        "Failed to fetch XYH from API: ${result.error.message}"
                    Log.e("OrderViewModel", errorMsg, result.error)
                    processRecorder?.recordProcess(
                        "XYH业务参数获取异常: ${result.error.message}，停止流程(getXyhBizParamsAndUpdateService)",
                        "ERROR"
                    )
                    XyhFetchResult.Error(errorMsg)
                }

            }
        } catch (e: Exception) {
            val errorMsg = "Exception occurred while fetching XYH."
            Log.e("OrderViewModel", errorMsg, e)
            processRecorder?.recordProcess(
                "XYH业务参数获取异常: ${e.message}，停止流程(getXyhBizParamsAndUpdateService)",
                "ERROR"
            )
            return XyhFetchResult.Error("$errorMsg: ${e.message}")
        }
    }

    /**
     * 处理地址请求结果，更新地址列表及UI显示
     * @param addressResult 地址API的请求结果
     */
    private fun processAddressResult(addressResult: RequestResult<String>?) {
        when (addressResult) {
            is RequestResult.Success -> {
                val addressData = addressResult.data
                try {
                    val addressResponse = ResponseParserUtils.parseAddressResponse(addressData)
                    if (addressResponse != null && addressResponse.code == 0) {
                        val addressList = addressResponse.data?.list
                        if (!addressList.isNullOrEmpty()) {
                            _addressList.value = addressList
                            _selectedAddressItem.value = addressList[0] // 设置第一个地址为选中地址
                            updateAddressDisplayText(addressList[0]) // 使用第一个地址更新显示
                            processRecorder?.recordProcess("地址列表获取成功: 共${addressList.size}个")
                            //showToast("地址获取成功")
                        } else {
                            setAddressDisplayText("地址列表为空")
                            _addressList.value = emptyList()
                            _selectedAddressItem.value = null // 清空选中地址
                            processRecorder?.recordProcess(
                                "地址列表为空，停止流程(processAddressResult)",
                                "ERROR"
                            )
                            showToast("地址列表为空")
                        }
                    } else {
                        val msg =
                            addressResponse?.message ?: "地址获取错误 (解析响应码非0或数据错误)"
                        setAddressDisplayText("地址获取错误")
                        _addressList.value = emptyList()
                        _selectedAddressItem.value = null // 清空选中地址
                        processRecorder?.recordProcess(
                            "地址获取错误: $msg，停止流程(processAddressResult)",
                            "ERROR"
                        )
                        showToast(msg)
                    }
                } catch (e: Exception) {
                    Log.e("OrderViewModel", "Failed to parse address data", e)
                    setAddressDisplayText("地址解析错误")
                    _addressList.value = emptyList()
                    _selectedAddressItem.value = null // 清空选中地址
                    processRecorder?.recordProcess(
                        "地址解析错误: ${e.message}，停止流程(processAddressResult)",
                        "ERROR"
                    )
                    showToast("${e.message}")
                }
            }

            is RequestResult.Error -> {
                Log.e("OrderViewModel", "Failed to fetch addresses from API", addressResult.error)
                setAddressDisplayText("地址获取异常")
                _addressList.value = emptyList()
                _selectedAddressItem.value = null // 清空选中地址
                processRecorder?.recordProcess(
                    "地址API请求错误: ${addressResult.error.message}，停止流程(processAddressResult)",
                    "ERROR"
                )
                showToast("${addressResult.error.message}")
            }

            null -> {
                setAddressDisplayText("服务未初始化")
                _addressList.value = emptyList()
                _selectedAddressItem.value = null // 清空选中地址
                processRecorder?.recordProcess(
                    "服务未初始化，无法获取地址，停止流程(processAddressResult)",
                    "ERROR"
                )
                showToast("服务未初始化")
            }
        }
    }

    /**
     * 根据地址项更新地址显示文本
     * @param addressItem 地址项
     */
    fun updateAddressDisplayText(addressItem: AddressItem) {
        val area = addressItem.address.area
        val city = addressItem.address.city
        val province = addressItem.address.provname
        val deliverydesc = addressItem.deliverydesc
        val detail = addressItem.address.detail
        val name = addressItem.name
        val phone = addressItem.phone
        val isdefault = addressItem.isdefault
        val isdefaultText = if (isdefault == 1) " 默认地址" else ""
        val deliveryPrefix = if (deliverydesc.isNotEmpty()) "$deliverydesc " else ""
        setAddressDisplayText("$area$detail $name,$phone $province$city $deliveryPrefix$isdefaultText")
    }

    /**
     * 处理店铺请求结果，更新店铺列表、UI显示及服务配置
     * @param shopResult 店铺API的请求结果
     */
    private fun processShopResult(shopResult: RequestResult<String>?) {
        when (shopResult) {
            is RequestResult.Success -> {
                val shopData = shopResult.data
                try {
                    val shopResponse = ResponseParserUtils.parseShopResponse(shopData)
                    if (shopResponse != null && shopResponse.code == 0) {
                        val cityId = shopResponse.data?.city?.id
                        val district = shopResponse.data?.address?.get(0)?.address?.district
                        val shopInfoList = shopResponse.data?.seller
                        if (!shopInfoList.isNullOrEmpty()) {
                            _shopList.value = shopInfoList
                            _currentShopItem.value = shopInfoList[0]
                            updateShopDisplayTextAndService(
                                shopInfoList[0],
                                cityId,
                                district
                            ) // 使用第一个店铺更新显示和服务
                            //processRecorder?.recordProcess("店铺列表: 共${shopInfoList.size}个店铺")
                            //showToast("店铺获取成功")
                        } else {
                            setShopDisplayText("没有找到可用店铺")
                            _shopList.value = emptyList()
                            processRecorder?.recordProcess(
                                "没有找到可用店铺，停止流程(processShopResult)",
                                "ERROR"
                            )
                            showToast("没有找到可用店铺")
                        }
                    } else {
                        val msg =
                            shopResponse?.message ?: "店铺获取错误 (解析响应码非0或数据错误)"
                        setShopDisplayText("店铺获取错误: $msg")
                        _shopList.value = emptyList()
                        processRecorder?.recordProcess(
                            "店铺获取错误: $msg，停止流程(processShopResult)",
                            "ERROR"
                        )
                        showToast("店铺获取错误: $msg")
                    }
                } catch (e: Exception) {
                    Log.e("OrderViewModel", "Failed to parse shop data", e)
                    setShopDisplayText("店铺信息解析错误")
                    _shopList.value = emptyList()
                    processRecorder?.recordProcess(
                        "店铺信息解析错误: ${e.message}，停止流程(processShopResult)",
                        "ERROR"
                    )
                    showToast("店铺信息解析错误: ${e.message}")
                }
            }

            is RequestResult.Error -> {
                Log.e("OrderViewModel", "Failed to fetch shops from API", shopResult.error)
                setShopDisplayText("店铺获取异常")
                _shopList.value = emptyList()
                processRecorder?.recordProcess(
                    "店铺API请求错误: ${shopResult.error.message}，停止流程(processShopResult)",
                    "ERROR"
                )
                showToast("店铺获取异常: ${shopResult.error.message}")
            }

            null -> {
                setShopDisplayText("服务未初始化")
                _shopList.value = emptyList()
                processRecorder?.recordProcess(
                    "服务未初始化，无法获取店铺，停止流程(processShopResult)",
                    "ERROR"
                )
                showToast("服务未初始化")
            }
        }
    }

    /**
     * 根据店铺信息更新店铺显示文本和RequestService配置
     * @param shopInfo 店铺信息
     * @param cityId 城市ID (可能为null)
     * @param district 地区 (可能为null)
     */
    private fun updateShopDisplayTextAndService(
        shopInfo: ShopInfo,
        cityId: String?,
        district: String?
    ) {
        val shopName = shopInfo.shopname
        val sellerName = shopInfo.sellername
        val distance = shopInfo.distance
        val businessTime = "${shopInfo.businessstart}-${shopInfo.businessend}"
        val isDeliveryToday = if (shopInfo.isDeliveryToday == 1) "今日可送" else "明日送"

        val currentShopId = shopInfo.shopid
        val currentSellerId = shopInfo.sellerid.toString()

        requestService?.setShopId(currentShopId)
        requestService?.setSellerId(currentSellerId)
        cityId?.let { requestService?.setCityId(it) }
        district?.let {
            requestService?.setDistrict(it)
            _selectedDistrict.value = district
        }

        processRecorder?.recordProcess("店铺信息: $sellerName-$shopName ${distance}米 $businessTime $isDeliveryToday")
        setShopDisplayText("$sellerName-$shopName ${distance}米 $businessTime $isDeliveryToday")
    }

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        viewModelScope.launch(Dispatchers.Main) {
            Toast.makeText(getApplication(), message, Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 从URI导入账号
     * @param uri 导入文件的URI
     */
    fun importAccountsFromUri(uri: Uri) {
        _accountImporting.value = true
        // 设置导入状态，暂停数据库查询
        ProgressManager.setImportingState(true)
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    // 读取文件内容
                    val content =
                        appContext.contentResolver.openInputStream(uri)?.use { inputStream ->
                            inputStream.bufferedReader().use { it.readText() }
                        } ?: throw Exception("读取文件错误")

                    // 检查是否为删除订单备份文件
                    if (DeletedOrderUtils.isDeletedOrderBackupFormat(content)) {
                        // 恢复删除订单数据
                        val restoreResult =
                            DeletedOrderUtils.restoreDeletedOrdersFromBackup(appContext, content)
                        if (restoreResult.success) {
                            showToast(restoreResult.message)
                        } else {
                            showToast("恢复错误: ${restoreResult.message}")
                        }
                        return@withContext
                    }

                    // 使用TokenParserUtil解析账号
                    val parseResult = TokenParserUtils.parseTokensFromContent(content, "order")

                    if (parseResult.tokens.isEmpty()) {
                        showToast("非合法数据")
                        return@withContext
                    }

                    var successCount = 0
                    var updateCount = 0
                    var failCount = 0

                    // 插入解析出的账号
                    parseResult.tokens.forEach { token ->
                        if (token is OrderTokenEntity) {
                            try {
                                val result = tokenRepository.insertToken(token)
                                if (result.first) {
                                    successCount++
                                } else {
                                    updateCount++
                                    Log.e(
                                        "OrderViewModel",
                                        "Failed to import token: ${result.second}"
                                    )
                                }
                            } catch (e: Exception) {
                                failCount++
                                Log.e("OrderViewModel", "Error importing token: ${e.message}")
                            }
                        }
                    }
                    
                    showToast(
                        "成功导入${successCount}个账号，更新${updateCount}个" +
                                (if (failCount > 0) "，失败${failCount}个" else "")
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
                showToast("导入异常: ${e.message}")
            } finally {
                _accountImporting.value = false
                // 导入完成，恢复数据库查询
                ProgressManager.setImportingState(false)
            }
        }
    }

    /**
     * 清空所有账号
     */
    fun clearAllOrderTokens() {
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    tokenRepository.deleteAllOrderTokens()
                }
                showToast("已清空所有账号")
            } catch (e: Exception) {
                e.printStackTrace()
                showToast("清空失败: ${e.message}")
            }
        }
    }

    /**
     * 删除指定范围的账号
     * @param startIndex 起始索引（从1开始）
     * @param endIndex 结束索引（从1开始）
     */
    fun clearOrderTokensRange(startIndex: Int, endIndex: Int) {
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val offset = startIndex - 1
                    val count = endIndex - startIndex + 1

                    val deletedCount = tokenRepository.deleteOrderTokensRange(offset, count)

                    Log.d(
                        "OrderViewModel",
                        "Deleted $deletedCount tokens from index $startIndex to $endIndex"
                    )
                    processRecorder?.recordProcess(
                        "账号删除范围: $startIndex 到 $endIndex ($deletedCount 个)",
                        "INFO"
                    )
                }
                showToast("已删除第 $startIndex 到 $endIndex 范围的账号")
            } catch (e: Exception) {
                e.printStackTrace()
                showToast("删除错误: ${e.message}")
            }
        }
    }

    /**
     * 处理购物车请求结果
     */
    private fun processCartResult(cartResult: RequestResult<String>?) {
        when (cartResult) {
            is RequestResult.Success -> {
                try {
                    val cartResponse = ResponseParserUtils.parseCartResponse(cartResult.data)
                    if (cartResponse != null && cartResponse.code == 0) {
                        val cartItems = cartResponse.data?.cartlist
                        if (!cartItems.isNullOrEmpty()) {
                            _cartList.value = cartItems
                            updateCartDisplayText(cartResponse.data)
                            //showToast("购物车获取成功")

                            // 设置当前选中的购物车
                            processRecorder?.recordProcess("购物车: 共${cartItems.size}个购物车")
                            _currentCartItem.value = _cartList.value.firstOrNull()
                        } else {
                            setCartDisplayText("购物车为空")
                            _cartList.value = emptyList()
                            processRecorder?.recordProcess("购物车: 购物车为空", "WARNING")
                            showToast("购物车为空")

                            // 设置当前选中的购物车为空
                            _currentCartItem.value = null

                            // 如果购物车对话框显示且购物车为空，则关闭购物车对话框
                            if (_showCartDialog.value) {
                                _showCartDialog.value = false
                            }
                        }
                    } else {
                        val msg =
                            cartResponse?.message ?: "购物车获取错误 (解析响应码非0或数据错误)"
                        //setCartDisplayText(msg)
                        //_cartList.value = emptyList()
                        val cartItems = cartResponse?.data?.cartlist
                        if (!cartItems.isNullOrEmpty()) {
                            _cartList.value = cartItems
                            updateCartDisplayText(cartResponse.data)
                            processRecorder?.recordProcess("购物车: 共${cartItems.size}个购物车")
                            //showToast("购物车获取成功")
                        } else {
                            setCartDisplayText("购物车为空")
                            _cartList.value = emptyList()
                            showToast("购物车为空")
                            processRecorder?.recordProcess("购物车: 购物车为空", "WARNING")
                        }
                        processRecorder?.recordProcess("购物车获取错误: $msg", "ERROR")
                        showToast(msg)
                    }
                } catch (e: Exception) {
                    Log.e("OrderViewModel", "Failed to parse cart data", e)
                    setCartDisplayText("购物车解析错误")
                    processRecorder?.recordProcess("购物车解析错误: ${e.message}", "ERROR")
                    _cartList.value = emptyList()
                    showToast("${e.message}")
                } finally {
                    updateSelectedCartItemsState()
                }
            }

            is RequestResult.Error -> {
                Log.e("OrderViewModel", "Failed to fetch cart from API", cartResult.error)
                setCartDisplayText("购物车获取异常")
                _cartList.value = emptyList()
                showToast("${cartResult.error.message}")
                processRecorder?.recordProcess(
                    "购物车API请求错误: ${cartResult.error.message}，停止流程(processCartResult)",
                    "ERROR"
                )
            }

            null -> {
                setCartDisplayText("服务未初始化")
                _cartList.value = emptyList()
                showToast("服务未初始化")
                processRecorder?.recordProcess(
                    "服务未初始化，无法获取购物车，停止流程(processCartResult)",
                    "ERROR"
                )
            }
        }
    }

    /**
     * 根据购物车数据更新购物车显示文本
     * @param cartDataObj 购物车数据对象 (CartData)
     */
    private fun updateCartDisplayText(cartDataObj: CartData) {
        val totalPrice = formatPrice(cartDataObj.cartTotalPrice)
        val totalDiscount = formatPrice(cartDataObj.cartTotalDiscount)
        // val promotionMsg = cartDataObj.cartPromotionMsg // 根据需求决定是否使用
        // val shopName = if (_cartList.value.isNotEmpty() && _cartList.value[0].shopname.isNotEmpty()) _cartList.value[0].shopname else "" // 根据需求决定是否使用

        processRecorder?.recordProcess("购物车总价¥$totalPrice 已优惠¥$totalDiscount")
        val displayText =
            "¥$totalPrice ${if (totalDiscount != "0.00") "已优惠¥$totalDiscount" else ""}".trim()
        setCartDisplayText(displayText)
    }

    /**
     * 格式化价格 (分转元)
     */
    @SuppressLint("DefaultLocale")
    private fun formatPrice(priceInCents: Int): String {
        return String.format("%.2f", priceInCents / 100.0)
    }

    /**
     * ViewModel工厂类
     */
    class Factory(
        private val tokenRepository: TokenRepository,
        private val application: Application
    ) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(OrderViewModel::class.java)) {
                return OrderViewModel(tokenRepository, application) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }

    /**
     * XYH参数获取结果的密封类
     */
    private sealed class XyhFetchResult {
        data object Success : XyhFetchResult() // 表示成功获取并设置了XYH参数
        data class Error(val errorMessage: String) : XyhFetchResult() // 表示获取XYH参数错误
    }

    /**
     * 当地址列表为空时，清空店铺和购物车的显示及数据。
     */
    private fun clearShopAndCartDisplayOnAddressEmpty() {
        setShopDisplayText("店铺未初始化")
        _shopList.value = emptyList()
        setCartDisplayText("购物车未同步")
        _cartList.value = emptyList()
        // Toast 已在 processAddressResult 中处理
        // 当地址为空时，也应该清空选中的地址项
        _selectedAddressItem.value = null
    }

    /**
     * 处理XYH参数获取失败的情况，更新购物车UI并显示Toast。
     * @param errorMessage 错误信息
     */
    private fun handleXyhFetchError(errorMessage: String) {
        Log.e("OrderViewModel", "XYH business parameters fetch failed: $errorMessage")
        setCartDisplayText("XYH业务参数错误")
        _cartList.value = emptyList() // 清空购物车列表
        showToast("XYH业务参数错误")
    }

    /**
     * 切换地址
     * 处理地址切换后的操作：更新地址显示、获取店铺信息、获取XYH业务参数、获取购物车
     * @param addressItem 选择的地址项
     */
    fun switchAddress(addressItem: AddressItem) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化，无法切换地址")
                return@launch
            }
            setLoadingTokenInfo(true)
            try {
                // 1. 立即在主线程更新地址显示文本
                processRecorder?.recordProcess("切换地址: ${addressItem.address}")
                withContext(Dispatchers.Main) {
                    _selectedAddressItem.value = addressItem // 更新选中的地址项
                    updateAddressDisplayText(addressItem)
                }
                // 2. 获取与新地址相关的店铺、XYH参数和购物车数据
                fetchShopCartAndXyhForAddress(addressItem, currentRequestService)

            } catch (e: Exception) {
                Log.e("OrderViewModel", "Error occurred while switching address", e)
                processRecorder?.recordProcess(
                    "切换地址异常: ${e.message}，停止流程(switchAddress)",
                    "ERROR"
                )
                withContext(Dispatchers.Main) {
                    setAddressDisplayText("切换地址错误") // 保留当前选中的地址的错误提示
                    // 清空后续依赖的数据显示
                    setShopDisplayText("切换地址错误")
                    _shopList.value = emptyList()
                    setCartDisplayText("切换地址错误")
                    _cartList.value = emptyList()
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 获取特定类型的购物车项目
     * @param type 购物车类型，可以是"normal"或"presale"
     * @return 过滤后的购物车项目列表
     */
    fun getCartItemsByType(type: String): List<CartItem> {
        return _cartList.value.filter {
            when (type) {
                "normal" -> it.type == "normal" || it.type.isEmpty()
                "presale" -> it.type == "presale"
                else -> true
            }
        }
    }

    /**
     * 增加购物车商品数量
     *
     * @param product 商品
     */
    fun addCartItemQuantity(product: Product) {
        changeCartItemQuantity(product, "1")
    }

    /**
     * 减少购物车商品数量
     *
     * @param product 商品
     */
    fun reduceCartItemQuantity(product: Product) {
        changeCartItemQuantity(product, "-1")
    }

    /**
     * 修改购物车商品数量
     *
     * @param product 商品
     * @param changeValue 变化值（正数为增加，负数为减少）
     */
    private fun changeCartItemQuantity(product: Product, changeValue: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            val selectedAddress = _selectedAddressItem.value
            if (selectedAddress == null) {
                showToast("请先选择地址")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                processRecorder?.recordProcess("修改商品: ${product.id}, 变化值: $changeValue")
                Log.d("OrderViewModel", "修改商品: ${product.id}, 变化值: $changeValue")

                val cartResult = withContext(Dispatchers.IO) {
                    currentRequestService.cart.changeCart(
                        "num",
                        selectedAddress,
                        changeValue,
                        product
                    )
                }


                withContext(Dispatchers.Main) {

                    processCartResult(cartResult)

                    when (cartResult) {
                        is RequestResult.Success -> {
                            val response =
                                ResponseParserUtils.parseResponse<Map<String, Any>>(cartResult.data)
                            if (response != null && response.isSuccess) {
                                processRecorder?.recordProcess("修改商品数量成功: ${product.id}, 变化值: $changeValue")
                                Log.d("OrderViewModel", "Successfully changed item quantity")
                            } else {
                                processRecorder?.recordProcess(
                                    "修改商品数量错误: ${product.id}, 变化值: $changeValue",
                                    "ERROR"
                                )
                                Log.e(
                                    "OrderViewModel",
                                    "Failed to change item quantity: ${response?.errorMessage}"
                                )
                                //showToast(response?.errorMessage ?: "未知错误")
                            }
                        }

                        is RequestResult.Error -> {
                            processRecorder?.recordProcess(
                                "修改商品数量异常: ${product.id}, 变化值: $changeValue",
                                "ERROR"
                            )
                            Log.e(
                                "OrderViewModel",
                                "Failed to change item quantity",
                                cartResult.error
                            )
                            showToast("${cartResult.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Error changing item quantity", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 更新购物车是否有选中商品的状态
     * 这个方法应在任何可能改变购物车选中状态的操作后调用
     */
    private fun updateSelectedCartItemsState() {
        // 重置购物车选中状态
        _hasSelectedCartItems.value = false

        // 获取所有商品模型
        val models = mutableListOf<CartModelWrapper>()
        _currentCartItem.value?.cartModels?.forEach { model ->
            when (model.modelType) {
                CartModelTypes.PRODUCT_ITEM -> {
                    models.add(
                        CartModelWrapper(
                            data = model.data,
                            modelType = model.modelType
                        )
                    )
                }
            }
        }

        // 检查是否有任何购物车商品被选中
        _hasSelectedCartItems.value = models.any {
            val product = it.getProduct()
            (product?.selectstate ?: 0) == 1
        }
    }


    /**
     * 删除购物车商品
     *
     * @param product 商品
     */
    fun deleteCartItem(product: Product) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            val selectedAddress = _selectedAddressItem.value
            if (selectedAddress == null) {
                showToast("请先选择地址")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                processRecorder?.recordProcess("删除购物车商品: ${product.id}")
                Log.d("OrderViewModel", "删除购物车商品: ${product.id}")

                val cartResult = withContext(Dispatchers.IO) {
                    currentRequestService.cart.clearCart(product, selectedAddress)
                }

                withContext(Dispatchers.Main) {

                    // 解析商品数据
                    processCartResult(cartResult)

                    when (cartResult) {
                        is RequestResult.Success -> {
                            val response =
                                ResponseParserUtils.parseResponse<Map<String, Any>>(cartResult.data)
                            if (response != null && response.isSuccess) {
                                processRecorder?.recordProcess("删除购物车商品成功: ${product.id}")
                                Log.d("OrderViewModel", "Successfully deleted item from cart")
                                showToast("已删除商品")
                                // 关闭删除确认对话框
                                setShowDeleteCartItemConfirmDialog(false)
                            } else {
                                processRecorder?.recordProcess(
                                    "删除购物车商品错误: ${product.id}",
                                    "ERROR"
                                )
                                Log.e(
                                    "OrderViewModel",
                                    "Failed to delete item from cart: ${response?.errorMessage}"
                                )
                                showToast(response?.errorMessage ?: "未知错误")
                            }
                        }

                        is RequestResult.Error -> {
                            processRecorder?.recordProcess(
                                "删除购物车商品异常: ${product.id}",
                                "ERROR"
                            )
                            Log.e(
                                "OrderViewModel",
                                "Failed to delete item from cart",
                                cartResult.error
                            )
                            showToast("${cartResult.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("删除购物车商品异常: ${product.id}", "ERROR")
                Log.e("OrderViewModel", "Error deleting item from cart", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 根据用户选择的选项重新提交订单
     * 在结算界面选项改变时调用
     *
     * @param selectedCoupons 选择的优惠券，JSON字符串格式
     * @param selectedRedPackets 选择的红包，JSON字符串格式
     * @param balancePayOption 余额支付选项，0或1
     * @param pointPayOption 积分支付选项，0或1
     * @param pickSelf 是否自提，0或1
     */
    fun orderPlaceWithOptions(
        selectedCoupons: String = "",
        selectedRedPackets: String = "",
        balancePayOption: Int = 0,
        pointPayOption: Int = 0,
        pickSelf: Int = 0
    ) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            val selectedAddress = _selectedAddressItem.value
            if (selectedAddress == null) {
                showToast("请先选择地址")
                return@launch
            }

            val currentCartItem = _currentCartItem.value
            if (currentCartItem == null) {
                showToast("未选中购物车")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用OrderPlace接口，传递用户选择的选项
                val orderResult = withContext(Dispatchers.IO) {
                    currentRequestService.order.orderPlace(
                        selectedAddress = selectedAddress,
                        selectedCart = currentCartItem,
                        firstOrderPlace = false,
                        selectedCouponCodeArr = selectedCoupons,
                        selectedRedPacketCodeArr = selectedRedPackets,
                        balancepayoption = balancePayOption.toString(),
                        pointpayoption = pointPayOption.toString(),
                        ispickself = pickSelf.toString(),
                        onRefreshOrderPlaceParams = { url, orderPlaceBody ->
                            currentRequestService.setOrderPlaceUrl(url)
                            currentRequestService.setOrderPlaceBody(orderPlaceBody)
                        }
                    )
                }

                // 处理OrderPlace结果
                withContext(Dispatchers.Main) {
                    when (orderResult) {
                        is RequestResult.Success -> {
                            val response =
                                ResponseParserUtils.parseOrderPlaceResponse(orderResult.data)
                            if (response != null && response.code == 0) {
                                _orderPlaceResponse.value = response
                                _showOrderPlaceDialog.value = true
                                processRecorder?.recordProcess("OrderPlace成功，应用新选项,selectedCoupons:$selectedCoupons,selectedRedPackets:$selectedRedPackets,balancePayOption:$balancePayOption,pointPayOption:$pointPayOption,pickSelf:$pickSelf")
                                Log.d("OrderViewModel", "OrderPlace成功，应用新选项")
                            } else {
                                processRecorder?.recordProcess(
                                    "重新OrderPlace错误: ${response?.message}",
                                    "ERROR"
                                )
                                Log.e("OrderViewModel", "OrderPlace错误: ${response?.message}")
                                showToast(response?.message ?: "未知错误")
                            }
                        }

                        is RequestResult.Error -> {
                            processRecorder?.recordProcess(
                                "OrderPlace请求出错: ${orderResult.error.message}",
                                "ERROR"
                            )
                            Log.e("OrderViewModel", "OrderPlace请求出错", orderResult.error)
                            showToast(orderResult.error.message ?: "请求异常")
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("OrderPlace操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "OrderPlace操作异常", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 处理结算操作
     */
    fun orderPlace() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            val selectedAddress = _selectedAddressItem.value
            if (selectedAddress == null) {
                showToast("请先选择地址")
                return@launch
            }

            val currentCartItem = _currentCartItem.value
            if (currentCartItem == null) {
                showToast("未选中购物车")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用OrderPlace接口
                val orderResult = withContext(Dispatchers.IO) {
                    currentRequestService.order.orderPlace(
                        selectedAddress = selectedAddress,
                        selectedCart = currentCartItem,
                        firstOrderPlace = true,
                        onRefreshOrderPlaceParams = { url, orderPlaceBody ->
                            currentRequestService.setOrderPlaceUrl(url)
                            currentRequestService.setOrderPlaceBody(orderPlaceBody)
                        }
                    )
                }

                // 处理OrderPlace结果
                withContext(Dispatchers.Main) {
                    when (orderResult) {
                        is RequestResult.Success -> {
                            val response =
                                ResponseParserUtils.parseOrderPlaceResponse(orderResult.data)
                            if (response != null && response.code == 0) {
                                _orderPlaceResponse.value = response
                                _showOrderPlaceDialog.value = true
                                processRecorder?.recordProcess("OrderPlace成功")
                            } else {
                                processRecorder?.recordProcess(
                                    "OrderPlace错误: ${response?.message}",
                                    "ERROR"
                                )
                                Log.e("OrderViewModel", "OrderPlace错误: ${response?.message}")
                                showToast(response?.message ?: "未知错误")
                            }
                        }

                        is RequestResult.Error -> {
                            processRecorder?.recordProcess(
                                "OrderPlace请求出错: ${orderResult.error.message}",
                                "ERROR"
                            )
                            Log.e("OrderViewModel", "OrderPlace请求出错", orderResult.error)
                            showToast(orderResult.error.message ?: "请求异常")
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("OrderPlace操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "OrderPlace操作异常", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 确认提交订单操作
     */
    fun orderConfirm(
        orderPlaceData: OrderPlaceData,
        tempOrderComment: String,
        expectTime: String,
        genPayUrl: Boolean
    ) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                processRecorder?.recordProcess("确认订单, 备注: ${tempOrderComment.ifEmpty { "备注为空" }}, 期望时间: $expectTime")
                Log.d("OrderViewModel", "Beginning order confirmation process")

                // 调用OrderService的orderConfirm方法提交订单
                val orderResult = withContext(Dispatchers.IO) {
                    currentRequestService.order.orderConfirm(
                        orderPlaceData = orderPlaceData,
                        comment = tempOrderComment,
                        texpecttime = expectTime
                    )
                }

                // 处理订单确认结果
                withContext(Dispatchers.Main) {
                    when (orderResult) {
                        is RequestResult.Success -> {
                            val response =
                                ResponseParserUtils.parseOrderConfirmResponse(orderResult.data)
                            if (response != null && response.code == 0) {
                                val payendtime = DateUtils.formatDate(
                                    response.data.payendtime,
                                    DateUtils.DatePattern.HH_MM_SS
                                )
                                processRecorder?.recordProcess("订单确认成功，支付结束时间: $payendtime")
                                if (genPayUrl && response.data.continuepay == 1) {
                                    orderPrePay(response.data.orderid, "pay.dcep.app.pay.and.sign")
                                }
                                Log.d("OrderViewModel", "Order confirmed successfully")
                                // 关闭OrderPlace对话框
                                setShowOrderPlaceDialog(false)
                                // 清空订单响应数据
                                setOrderPlaceResponse(null)
                                // 更新购物车信息（订单提交后购物车应该被清空）
                                refreshCart()
                                // 刷新订单列表
                                getOrderList()
                                showToast("提交成功")
                            } else {
                                processRecorder?.recordProcess(
                                    "订单确认错误: ${response?.message}",
                                    "ERROR"
                                )
                                Log.e(
                                    "OrderViewModel",
                                    "Failed to confirm order: ${response?.message}"
                                )
                                showToast(response?.message ?: "未知错误")
                            }
                        }

                        is RequestResult.Error -> {
                            processRecorder?.recordProcess(
                                "订单确认请求异常: ${orderResult.error.message}",
                                "ERROR"
                            )
                            Log.e(
                                "OrderViewModel",
                                "Order confirmation request error",
                                orderResult.error
                            )
                            showToast("${orderResult.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("订单确认操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception during order confirmation", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 清空地址
     * 该函数会循环删除所有地址项
     */
    fun clearAddress() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            val currentAddressList = _addressList.value
            if (currentAddressList.isEmpty()) {
                processRecorder?.recordProcess("地址列表为空", "WARNING")
                showToast("地址列表为空")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                var successCount = 0
                var failCount = 0

                withContext(Dispatchers.IO) {
                    // 依次删除每个地址
                    for (addressItem in currentAddressList) {
                        val result = currentRequestService.address.deleteAddress(addressItem.id)

                        when (result) {
                            is RequestResult.Success -> {
                                val response =
                                    ResponseParserUtils.parseResponse<Map<String, Int>>(result.data)
                                if (response != null && response.isSuccess) {
                                    processRecorder?.recordProcess("地址删除成功: ${addressItem.id}")
                                    Log.d(
                                        "OrderViewModel",
                                        "Successfully deleted address: ${addressItem.id}"
                                    )
                                    successCount++
                                } else {
                                    processRecorder?.recordProcess(
                                        "地址删除错误: ${addressItem.id}",
                                        "ERROR"
                                    )
                                    Log.e(
                                        "OrderViewModel",
                                        "Failed to delete address: ${response?.errorMessage}"
                                    )
                                    failCount++
                                }
                            }

                            is RequestResult.Error -> {
                                processRecorder?.recordProcess(
                                    "地址删除异常: ${addressItem.id}",
                                    "ERROR"
                                )
                                Log.e("OrderViewModel", "Failed to delete address", result.error)
                                failCount++
                            }
                        }
                    }

                    withContext(Dispatchers.Main) {
                        // 提示删除结果
                        if (successCount > 0) {
                            showToast("成功删除${successCount}个地址" + (if (failCount > 0) "，${failCount}个失败" else ""))
                        } else {
                            showToast("地址删除失败")
                        }
                        processRecorder?.recordProcess("地址删除结果: 成功${successCount}个, 失败${failCount}个")
                        // 刷新地址列表
                        refreshAddressRelatedData()
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("地址删除操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Error clearing addresses", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 清空购物车所有商品
     */
    fun clearCart() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            val selectedAddress = _selectedAddressItem.value
            if (selectedAddress == null) {
                showToast("请先选择地址")
                return@launch
            }

            val currentCartList = _cartList.value
            if (currentCartList.isEmpty()) {
                processRecorder?.recordProcess("购物车已经为空", "WARNING")
                showToast("购物车已经为空")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {

                val cartResult = withContext(Dispatchers.IO) {
                    // 将整个购物车列表传递给cartClear方法进行批量删除
                    currentRequestService.cart.clearCart(currentCartList, selectedAddress)
                }

                withContext(Dispatchers.Main) {
                    // 处理请求结果并更新UI
                    when (cartResult) {
                        is RequestResult.Success -> {
                            val response =
                                ResponseParserUtils.parseResponse<Map<String, Any>>(cartResult.data)
                            if (response != null && response.isSuccess) {
                                processRecorder?.recordProcess("购物车清空成功")
                                Log.d("CartOperation", "购物车清空成功")
                                showToast("购物车已清空")

                                // 清空购物车列表并更新UI
                                _cartList.value = emptyList()
                                setCartDisplayText("购物车已清空")

                                // 更新购物车选中状态
                                updateSelectedCartItemsState()
                            } else {
                                processRecorder?.recordProcess(
                                    "清空购物车错误: ${response?.errorMessage}",
                                    "ERROR"
                                )
                                Log.e("CartOperation", "清空购物车错误: ${response?.errorMessage}")
                                showToast("${response?.errorMessage}")
                                // 刷新购物车数据，以确保UI是最新状态
                                refreshCart()
                            }
                        }

                        is RequestResult.Error -> {
                            processRecorder?.recordProcess(
                                "清空购物车异常: ${cartResult.error.message}",
                                "ERROR"
                            )
                            Log.e("CartOperation", "清空购物车异常", cartResult.error)
                            showToast("${cartResult.error.message}")
                            // 刷新购物车数据，以确保UI是最新状态
                            refreshCart()
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("清空购物车操作异常: ${e.message}", "ERROR")
                Log.e("CartOperation", "清空购物车异常", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                    // 刷新购物车数据，以确保UI是最新状态
                    refreshCart()
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 刷新地址信息及后续依赖数据（店铺、购物车等）
     * 此函数会：
     * 1. 重新获取地址列表。
     * 2. 如果地址列表获取成功且不为空，则使用第一个地址：
     *    a. 获取店铺信息。
     *    b. 获取XYH业务参数。
     *    c. 获取购物车信息。
     * 3. 更新UI显示。
     */
    fun refreshAddressRelatedData() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }
            setLoadingTokenInfo(true)
            try {
                // 1. 重置依赖于地址的参数
                currentRequestService.setXyhBizParams("")
                currentRequestService.setWebXyhBizParams("") // 也清空Web参数，它会在获取XYH时重新生成
                currentRequestService.setShopId("")
                currentRequestService.setSellerId("")
                // CityID 是从店铺信息获取的，这里不需要主动清除，会在 processShopResult 中更新

                // 2. 获取新的地址列表及后续依赖数据
                fetchAddressesAndThenDependentData(currentRequestService)

            } catch (e: Exception) {
                processRecorder?.recordProcess(
                    "刷新地址相关数据异常: ${e.message}，停止流程(refreshAddressRelatedData)",
                    "ERROR"
                )
                Log.e("OrderViewModel", "Error refreshing address related data", e)
                withContext(Dispatchers.Main) {
                    setAccountDisplayText(accountDisplayText.value) // 保留账号显示
                    setAddressDisplayText("刷新数据错误")
                    setShopDisplayText("刷新数据错误")
                    setCartDisplayText("刷新数据错误")
                    _addressList.value = emptyList()
                    _shopList.value = emptyList()
                    _cartList.value = emptyList()
                    _selectedAddressItem.value = null // 刷新出错时也清空选中地址
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 刷新购物车信息
     * 此函数会基于当前已选定的地址和已设置的XYH业务参数来重新获取购物车数据。
     */
    fun refreshCart() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }
            val currentSelectedAddress = _selectedAddressItem.value // 获取当前选中的地址
            if (currentSelectedAddress == null) {
                showToast("地址未同步")
                //setCartDisplayText("地址未同步") // 更新购物车显示文本
                _cartList.value = emptyList()      // 清空购物车列表
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                withContext(Dispatchers.IO) {
                    // 使用当前选中的地址的ID来获取购物车
                    val cartResult =
                        currentRequestService.cart.getAllCart(currentSelectedAddress.id)
                    withContext(Dispatchers.Main) {
                        processCartResult(cartResult)
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("刷新购物车异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Error refreshing cart", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    // --- 确认对话框方法 Start ---
    fun setShowClearAllTokensConfirmDialog(show: Boolean) {
        _showClearAllTokensConfirmDialog.value = show
    }

    fun setShowDeleteCartItemConfirmDialog(show: Boolean, product: Product? = null) {
        _cartItemProductToDelete.value = product // 在显示对话框前设置product
        _showDeleteCartItemConfirmDialog.value = show
    }

    fun clearAddressConfirmDialog(show: Boolean) {
        _showClearAddressConfirmDialog.value = show
    }

    fun setShowClearCartConfirmDialog(show: Boolean) {
        _showClearCartConfirmDialog.value = show
    }

    fun setShowDeleteAddressConfirmDialog(show: Boolean, addressItem: AddressItem? = null) {
        _addressToDelete.value = addressItem // 在显示对话框前设置addressItem
        _showDeleteAddressConfirmDialog.value = show
    }

    fun setShowClearOrderConfirmDialog(show: Boolean) {
        _showClearOrderConfirmDialog.value = show
    }
    // --- 确认对话框方法 End ---

    /**
     * 删除指定地址
     * @param addressItem 要删除的地址项
     */
    fun deleteAddressById(addressItem: AddressItem) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                withContext(Dispatchers.IO) {
                    // 调用API删除地址
                    val result = currentRequestService.address.deleteAddress(addressItem.id)

                    withContext(Dispatchers.Main) {
                        when (result) {
                            is RequestResult.Success -> {
                                // Parse response: {"code":0,"data":{"success":1},"message":"","now":1748858012980}
                                // Response indicates successful deletion with code 0 and success flag 1
                                val response =
                                    ResponseParserUtils.parseResponse<Map<String, Int>>(result.data)
                                if (response != null && response.isSuccess) {
                                    processRecorder?.recordProcess("地址删除成功: ${addressItem.id}")
                                    Log.d(
                                        "OrderViewModel",
                                        "Successfully deleted address: ${addressItem.id}"
                                    )
                                    showToast("地址删除成功")
                                    // 关闭地址菜单
                                    _showAddressMenu.value = false
                                    // 刷新地址列表
                                    fetchAddressesAndThenDependentData(currentRequestService)
                                } else {
                                    processRecorder?.recordProcess(
                                        "地址删除错误: ${addressItem.id}",
                                        "ERROR"
                                    )
                                    Log.e(
                                        "OrderViewModel",
                                        "Failed to delete address: ${response?.errorMessage}"
                                    )
                                    showToast("${response?.errorMessage}")
                                }
                            }

                            is RequestResult.Error -> {
                                processRecorder?.recordProcess(
                                    "地址删除异常: ${addressItem.id}",
                                    "ERROR"
                                )
                                Log.e("OrderViewModel", "Failed to delete address", result.error)
                                showToast("${result.error.message}")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("地址删除操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Error deleting address", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置显示添加地址对话框状态
     * @param show 是否显示
     */
    fun setShowAddAddressDialog(show: Boolean) {
        _showAddAddressDialog.value = show
    }

    /**
     * 设置显示添加商品对话框状态
     * @param show 是否显示
     */
    fun setShowAddProductDialog(show: Boolean) {
        _showAddProductDialog.value = show
    }

    /**
     * 更新地址参数字符串
     * @param addressStr 地址参数字符串
     */
    fun setAddAddressStr(addressStr: String) {
        _addAddressStr.value = addressStr
    }

    /**
     * 更新地址定位参数字符串
     * @param locationStr 定位参数字符串
     */
    fun setAddAddressLocationStr(locationStr: String) {
        _addAddressLocationStr.value = locationStr
    }

    /**
     * 更新随机详细地址正则表达式
     * @param regex 详细地址正则表达式
     */
    fun setRandomDetailsRegex(regex: String) {
        _randomDetailsRegex.value = regex
    }

    /**
     * 设置是否固定地址
     * @param fasten 是否固定
     */
    fun setFastenAddress(fasten: Boolean) {
        if (_selectedAddressItem.value == null) {
            showToast("选择地址为空")
            return
        }
        if (fasten) {
            showToast("地址已固定，将不能添加地址")
        }
        _fastenAddress.value = fasten
    }

    /**
     * 设置是否固定经纬度
     * @param fasten 是否固定
     */
    fun setFastenLatLng(fasten: Boolean) {
        _fastenLatLng.value = fasten
    }

    /**
     * 设置订单备注
     * @param comment 订单备注
     */
    fun setOrderComment(comment: String) {
        _orderComment.value = comment
    }

    /**
     * 设置是否生成支付URL
     * @param genPayUrl 是否生成支付URL
     */
    fun setGenPayUrl(genPayUrl: Boolean) {
        _genPayUrl.value = genPayUrl
    }

    /**
     * 保存地址配置
     * @param addressStr 地址参数字符串
     * @param locationStr 定位参数字符串
     * @param detailsRegex 详细地址正则表达式
     * @param comment 订单备注
     * @param shopItem 店铺信息
     */
    fun saveAddressConfig(
        addressStr: String,
        locationStr: String,
        detailsRegex: String,
        comment: String,
        shopItem: ShopInfo? = null
    ) {
        _addAddressStr.value = addressStr
        _addAddressLocationStr.value = locationStr
        _randomDetailsRegex.value = detailsRegex
        _orderComment.value = comment
        _addAddressShopItem.value = shopItem
        processRecorder?.recordProcess("地址配置保存成功: $addressStr, $locationStr, $detailsRegex, $comment, ${shopItem?.shopname}")
        Log.d("OrderViewModel", "Address configuration saved")
    }

    /**
     * 添加地址
     * @param addressStr 地址参数字符串
     * @param locationStr 定位参数字符串
     * @param name 收件人姓名
     * @param phone 收件人电话
     * @return 是否添加成功
     */
    fun addAddress(addressStr: String, locationStr: String, name: String, phone: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                withContext(Dispatchers.IO) {

                    // 调用API添加地址
                    val result = currentRequestService.address.addAddress(
                        addressStr,
                        locationStr,
                        name,
                        phone
                    )

                    withContext(Dispatchers.Main) {
                        when (result) {
                            is RequestResult.Success -> {
                                val response =
                                    ResponseParserUtils.parseResponse<Map<String, Any>>(result.data)
                                if (response != null && response.isSuccess) {
                                    processRecorder?.recordProcess("地址添加成功：${addressStr}")
                                    Log.d("OrderViewModel", "Successfully added address")
                                    showToast("地址添加成功")
                                    // 刷新地址列表
                                    refreshAddressRelatedData()
                                } else {
                                    processRecorder?.recordProcess(
                                        "地址添加错误: ${response?.errorMessage}",
                                        "ERROR"
                                    )
                                    Log.e(
                                        "OrderViewModel",
                                        "Failed to add address: ${response?.errorMessage}"
                                    )
                                    showToast("${response?.errorMessage}")
                                }
                            }

                            is RequestResult.Error -> {
                                processRecorder?.recordProcess(
                                    "地址添加异常: ${result.error.message}",
                                    "ERROR"
                                )
                                Log.e("OrderViewModel", "Failed to add address", result.error)
                                showToast("${result.error.message}")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("地址添加操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Error adding address", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 获取预配置地址列表
     * @return 预配置地址列表
     */
    fun getPreConfigAddressList(): List<JSONObject> {
        // 这里应该从数据库或配置文件中加载预设地址
        try {
            val preConfigList = mutableListOf<JSONObject>()

            /*val preConfig1 = JSONObject()
            preConfig1.put("preName", "众恒花园")
            preConfig1.put("randomDetailsRegexLs", "XX单元XX栋XX-X号")
            preConfig1.put(
                "addAddressStr",
                "{\"area\":\"众恒花园\",\"city\":\"福州\",\"cityid\":4,\"detail\":\"8楼8-4\",\"provname\":\"福建省\",\"district\":\"晋安区\",\"_uuid\":\"${UUID.randomUUID()}\"}"
            )
            preConfig1.put(
                "addAddressLocationStr",
                "{\"lat\":\"26.106931\",\"lng\":\"119.343242\",\"_uuid\":\"${UUID.randomUUID()}\"}"
            )
            preConfig1.put("comment", "")

            preConfigList.add(preConfig1)*/

            return preConfigList
        } catch (e: Exception) {
            Log.e("OrderViewModel", "Error creating preConfig address list", e)
            return emptyList()
        }
    }

    /**
     * 保存商品配置
     * @param inputString 商品配置字符串
     */
    fun saveProductConfig(inputString: String) {
        viewModelScope.launch {
            try {
                processRecorder?.recordProcess("商品配置保存成功: $inputString")
                Log.d("OrderViewModel", "Product config saved: $inputString")
                _productConfigStr.value = inputString
                showToast("商品配置已保存")
            } catch (e: Exception) {
                processRecorder?.recordProcess("商品配置保存异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Error saving product config", e)
                showToast("${e.message}")
            }
        }
    }

    /**
     * 添加商品到购物车
     * @param productConfigStr 商品配置字符串，格式为：id,数量[,goodstagid][,bundlepromocode]
     * 例如：12345,1 或 12345,2,0,PROMO
     */
    fun addProductToCart(productConfigStr: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            val selectedAddress = _selectedAddressItem.value
            if (selectedAddress == null) {
                showToast("请先选择地址")
                return@launch
            }

            // 验证输入格式
            if (productConfigStr.isBlank()) {
                showToast("商品配置不能为空")
                processRecorder?.recordProcess("商品配置不能为空", "WARNING")
                return@launch
            }

            if (!productConfigStr.contains(";") && !productConfigStr.contains(",")) {
                showToast("商品配置格式错误")
                processRecorder?.recordProcess("商品配置格式错误: $productConfigStr", "ERROR")
                return@launch
            }

            val productConfigArr = productConfigStr.split(";")

            setLoadingTokenInfo(true)
            try {
                var successCount = 0
                var failCount = 0

                withContext(Dispatchers.IO) {
                    // 遍历商品配置数组，逐个添加商品
                    for (productConfig in productConfigArr) {
                        if (productConfig.isBlank()) {
                            continue
                        }

                        // 调用CartService的addCartGoods方法添加单个商品到购物车
                        val result =
                            currentRequestService.cart.addCartGoods(productConfig, selectedAddress)

                        when (result) {
                            is RequestResult.Success -> {
                                val response =
                                    ResponseParserUtils.parseResponse<Map<String, Any>>(result.data)
                                if (response != null && response.isSuccess) {
                                    processRecorder?.recordProcess("商品添加成功: $productConfig")
                                    Log.d(
                                        "OrderViewModel",
                                        "Successfully added product to cart: $productConfig"
                                    )
                                    successCount++
                                } else {
                                    processRecorder?.recordProcess(
                                        "商品添加错误: $productConfig, ${response?.errorMessage}",
                                        "ERROR"
                                    )
                                    Log.e(
                                        "OrderViewModel",
                                        "Failed to add product to cart: $productConfig, ${response?.errorMessage}"
                                    )
                                    showToast("${response?.errorMessage}")
                                    failCount++
                                }
                            }

                            is RequestResult.Error -> {
                                processRecorder?.recordProcess(
                                    "商品添加异常: $productConfig, ${result.error.message}",
                                    "ERROR"
                                )
                                Log.e(
                                    "OrderViewModel",
                                    "Failed to add product to cart: $productConfig",
                                    result.error
                                )
                                showToast("${result.error.message}")
                                failCount++
                            }
                        }
                    }
                }

                withContext(Dispatchers.Main) {
                    if (successCount > 0) {
                        processRecorder?.recordProcess("商品添加结果: 成功${successCount}个, 失败${failCount}个")
                        showToast("成功添加${successCount}个商品" + (if (failCount > 0) "，${failCount}个发生错误" else ""))

                        // 关闭添加商品对话框
                        setShowAddProductDialog(false)

                        // 刷新购物车数据
                        refreshCart()
                    } else {
                        processRecorder?.recordProcess("商品添加结果: 全部错误", "ERROR")
                        showToast("添加商品全部错误")
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("商品添加操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Error adding products to cart", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 处理商品点击事件，点击选中或取消选中
     *
     * @param product 商品
     */
    fun onCartItemClick(product: Product) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                Log.e("CartOperation", "Service not initialized when trying to toggle cart item")
                showToast("服务未初始化")
                return@launch
            }

            val selectedAddress = _selectedAddressItem.value
            if (selectedAddress == null) {
                processRecorder?.recordProcess("请先选择地址(onCartItemClick)", "ERROR")
                Log.e("CartOperation", "No address selected when trying to toggle cart item")
                showToast("请先选择地址")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 切换商品的选中状态（0表示未选中，1表示选中）
                val newSelectState = if (product.selectstate == 0) "1" else "0"
                processRecorder?.recordProcess("切换商品选中状态: ${product.id}, 新状态: $newSelectState")
                Log.d(
                    "CartOperation",
                    "Toggling cart item ${product.id} select state to: $newSelectState"
                )

                // 调用CartService的changeCart方法修改选中状态
                val cartResult = withContext(Dispatchers.IO) {
                    currentRequestService.cart.changeCart(
                        "selectstate",
                        selectedAddress,
                        newSelectState,
                        product
                    )
                }

                // 处理请求结果并更新UI
                withContext(Dispatchers.Main) {
                    processCartResult(cartResult)
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("切换商品选中状态异常: ${e.message}", "ERROR")
                Log.e("CartOperation", "Error changing cart item select state", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    // --- AccountDialog 相关函数 Start ---
    fun setShowAccountDialog(show: Boolean) {
        _showAccountDialog.value = show
    }

    fun getAllCard() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 分别获取state=0和state=1的永辉卡列表
                val cardItems = mutableListOf<CardItem>()

                // 获取state=0的卡列表（未使用）
                val cardResult0 = withContext(Dispatchers.IO) {
                    currentRequestService.card.getCardList(state = "0", pageSize = "20")
                }

                // 处理state=0的结果
                when (cardResult0) {
                    is RequestResult.Success -> {
                        val cardListResponse =
                            ResponseParserUtils.parseCardListResponse(cardResult0.data)
                        if (cardListResponse != null && cardListResponse.code == 0) {
                            val items = cardListResponse.data?.list
                            if (!items.isNullOrEmpty()) {
                                cardItems.addAll(items)
                                processRecorder?.recordProcess("未使用永辉卡列表: 共${items.size}条")
                                Log.d(
                                    "OrderViewModel",
                                    "Successfully fetched ${items.size} cards with state=0"
                                )
                            }
                        } else {
                            val errorMsg = cardListResponse?.message ?: "获取未使用永辉卡列表错误"
                            processRecorder?.recordProcess(
                                "获取未使用永辉卡列表错误: $errorMsg",
                                "ERROR"
                            )
                            Log.e("OrderViewModel", "Failed to get card list (state=0): $errorMsg")
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取未使用永辉卡列表异常: ${cardResult0.error.message}",
                            "ERROR"
                        )
                        Log.e(
                            "OrderViewModel",
                            "Error fetching card list (state=0)",
                            cardResult0.error
                        )
                    }
                }

                // 获取state=1的卡列表（已使用）
                val cardResult1 = withContext(Dispatchers.IO) {
                    currentRequestService.card.getCardList(state = "1", pageSize = "20")
                }

                // 处理state=1的结果
                when (cardResult1) {
                    is RequestResult.Success -> {
                        val cardListResponse =
                            ResponseParserUtils.parseCardListResponse(cardResult1.data)
                        if (cardListResponse != null && cardListResponse.code == 0) {
                            val items = cardListResponse.data?.list
                            if (!items.isNullOrEmpty()) {
                                cardItems.addAll(items)
                                processRecorder?.recordProcess("已使用永辉卡列表: 共${items.size}条")
                                Log.d(
                                    "OrderViewModel",
                                    "Successfully fetched ${items.size} cards with state=1"
                                )
                            }
                        } else {
                            val errorMsg = cardListResponse?.message ?: "获取已使用永辉卡列表错误"
                            processRecorder?.recordProcess(
                                "获取已使用永辉卡列表错误: $errorMsg",
                                "ERROR"
                            )
                            Log.e("OrderViewModel", "Failed to get card list (state=1): $errorMsg")
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取已使用永辉卡列表异常: ${cardResult1.error.message}",
                            "ERROR"
                        )
                        Log.e(
                            "OrderViewModel",
                            "Error fetching card list (state=1)",
                            cardResult1.error
                        )
                    }
                }

                // 处理合并后的结果
                withContext(Dispatchers.Main) {
                    if (cardItems.isNotEmpty()) {
                        _cardList.value = cardItems
                        processRecorder?.recordProcess("永辉卡列表: 共${cardItems.size}条")
                        Log.d("OrderViewModel", "Total cards fetched: ${cardItems.size}")
                    } else {
                        processRecorder?.recordProcess("永辉卡列表: 未找到永辉卡", "WARNING")
                        Log.d("OrderViewModel", "No cards found")
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("获取永辉卡列表操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while fetching card list", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    fun getCardIndexInfo() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用CardService获取永辉卡总余额
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.card.getCardIndexInfo()
                }

                when (result) {
                    is RequestResult.Success -> {
                        // 使用ResponseParserUtils解析响应数据
                        val cardIndexInfoResponse =
                            ResponseParserUtils.parseCardIndexInfoResponse(result.data)
                        if (cardIndexInfoResponse != null && cardIndexInfoResponse.code == 0) {
                            // 获取余额并更新状态
                            val balance = cardIndexInfoResponse.data?.balance ?: "0"
                            processRecorder?.recordProcess("永辉卡余额: ¥$balance")
                            Log.d("OrderViewModel", "Card balance fetched: $balance")
                            _cardBalance.value = balance
                        } else {
                            val errorMsg = cardIndexInfoResponse?.message ?: "获取永辉卡余额错误"
                            processRecorder?.recordProcess("获取永辉卡余额错误: $errorMsg", "ERROR")
                            Log.e("OrderViewModel", "Failed to get card balance: $errorMsg")
                            showToast(errorMsg)
                            _cardBalance.value = "0"
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取永辉卡余额异常: ${result.error.message}",
                            "ERROR"
                        )
                        Log.e("OrderViewModel", "Error fetching card balance", result.error)
                        _cardBalance.value = "0"
                        showToast("获取永辉卡余额异常: ${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("获取永辉卡余额操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while fetching card balance", e)
                _cardBalance.value = "0"
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 获取优惠券列表
     * 按顺序获取红包券和通用优惠券，并合并结果
     */
    fun getCouponList() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 存储所有优惠券数据
                val allAvailableCoupons = mutableListOf<CouponListCoupon>()
                val allUnavailableCoupons = mutableListOf<CouponListCoupon>()
                val allCategories = mutableListOf<CouponCategory>()

                // 1. 首先获取红包券(redEnvelope.coupon)
                val redEnvelopeResult = withContext(Dispatchers.IO) {
                    currentRequestService.coupon.getCouponList("redEnvelope.coupon")
                }

                when (redEnvelopeResult) {
                    is RequestResult.Success -> {
                        val redEnvelopeCouponResponse =
                            ResponseParserUtils.parseCouponResponse(redEnvelopeResult.data)
                        if (redEnvelopeCouponResponse != null && redEnvelopeCouponResponse.code == 0) {
                            // 提取红包券数据
                            val availableCoupons =
                                redEnvelopeCouponResponse.data?.availablecoupons?.coupons
                                    ?: emptyList()
                            val unavailableCoupons =
                                redEnvelopeCouponResponse.data?.unavailablecoupons?.coupons
                                    ?: emptyList()
                            val categories =
                                redEnvelopeCouponResponse.data?.categories ?: emptyList()
                            val count = redEnvelopeCouponResponse.data?.count ?: 0

                            // 添加到总列表
                            allAvailableCoupons.addAll(availableCoupons)
                            allUnavailableCoupons.addAll(unavailableCoupons)
                            allCategories.addAll(categories)

                            processRecorder?.recordProcess("红包列表: 可用${availableCoupons.size}个, 不可用${unavailableCoupons.size}个")
                            Log.d(
                                "OrderViewModel",
                                "Successfully fetched red envelope coupons: available=${availableCoupons.size}, unavailable=${unavailableCoupons.size}"
                            )
                        } else {
                            val errorMsg = redEnvelopeCouponResponse?.message ?: "获取红包列表错误"
                            processRecorder?.recordProcess("获取红包列表错误: $errorMsg", "ERROR")
                            Log.e("OrderViewModel", "Failed to get red envelope coupons: $errorMsg")
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取红包列表异常: ${redEnvelopeResult.error.message}",
                            "ERROR"
                        )
                        Log.e(
                            "OrderViewModel",
                            "Error fetching red envelope coupons",
                            redEnvelopeResult.error
                        )
                        showToast("获取红包列表异常: ${redEnvelopeResult.error.message}")
                    }
                }

                // 2. 然后获取通用优惠券(coupon.general)
                val generalResult = withContext(Dispatchers.IO) {
                    currentRequestService.coupon.getCouponList("coupon.general")
                }

                when (generalResult) {
                    is RequestResult.Success -> {
                        val generalCouponResponse =
                            ResponseParserUtils.parseCouponResponse(generalResult.data)
                        if (generalCouponResponse != null && generalCouponResponse.code == 0) {
                            // 提取通用券数据
                            val availableCoupons =
                                generalCouponResponse.data?.availablecoupons?.coupons ?: emptyList()
                            val unavailableCoupons =
                                generalCouponResponse.data?.unavailablecoupons?.coupons
                                    ?: emptyList()
                            val categories = generalCouponResponse.data?.categories ?: emptyList()
                            val count = generalCouponResponse.data?.count ?: 0

                            // 添加到总列表
                            allAvailableCoupons.addAll(availableCoupons)
                            allUnavailableCoupons.addAll(unavailableCoupons)
                            // 注意：为避免类别重复，这里只添加第一次请求没有的类别
                            val existingCategoryValues = allCategories.map { it.value }
                            allCategories.addAll(categories.filter { it.value !in existingCategoryValues })

                            processRecorder?.recordProcess("优惠券列表: 可用${availableCoupons.size}个, 不可用${unavailableCoupons.size}个")
                            Log.d(
                                "OrderViewModel",
                                "Successfully fetched general coupons: available=${availableCoupons.size}, unavailable=${unavailableCoupons.size}"
                            )
                        } else {
                            val errorMsg = generalCouponResponse?.message ?: "获取优惠券列表错误"
                            processRecorder?.recordProcess("获取优惠券列表错误: $errorMsg", "ERROR")
                            Log.e("OrderViewModel", "Failed to get general coupons: $errorMsg")
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取优惠券列表异常: ${generalResult.error.message}",
                            "ERROR"
                        )
                        Log.e(
                            "OrderViewModel",
                            "Error fetching general coupons",
                            generalResult.error
                        )
                        showToast("获取优惠券列表异常: ${generalResult.error.message}")
                    }
                }

                // 更新UI状态
                withContext(Dispatchers.Main) {
                    _couponList.value = allAvailableCoupons.toMutableList()
                    _unavailableCouponList.value = allUnavailableCoupons.toMutableList()

                    processRecorder?.recordProcess("优惠券/红包列表: 可用${allAvailableCoupons.size}个, 不可用${allUnavailableCoupons.size}个")
                    Log.d(
                        "OrderViewModel",
                        "Total coupons fetched: available=${allAvailableCoupons.size}, unavailable=${allUnavailableCoupons.size}"
                    )

                    if (allAvailableCoupons.isEmpty() && allUnavailableCoupons.isEmpty()) {
                        processRecorder?.recordProcess("优惠券列表为空", "WARNING")
                        showToast("优惠券列表为空")
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("获取优惠券操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while fetching coupon list", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 获取助力券列表
     * 获取当前账号可用的助力券列表
     */
    fun getBoostCouponList() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API获取助力券列表
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.coupon.getBoostCouponList()
                }

                when (result) {
                    is RequestResult.Success -> {
                        // 使用ResponseParserUtils解析响应数据
                        val boostCouponResponse =
                            ResponseParserUtils.parseBoostCouponListResponse(result.data)
                        if (boostCouponResponse != null && boostCouponResponse.code == 0) {
                            // 更新状态
                            val boostCoupons = boostCouponResponse.data?.records ?: emptyList()

                            withContext(Dispatchers.Main) {
                                _boostCouponList.value = boostCoupons

                                processRecorder?.recordProcess("助力券列表: 共${boostCoupons.size}条")
                                Log.d(
                                    "OrderViewModel",
                                    "Successfully fetched boost coupons: ${boostCoupons.size}"
                                )

                                if (boostCoupons.isEmpty()) {
                                    processRecorder?.recordProcess(
                                        "获取助力券列表: 没有可用的助力券",
                                        "WARNING"
                                    )
                                    showToast("没有可用的助力券")
                                }
                            }
                        } else {
                            val errorMsg =
                                boostCouponResponse?.message ?: "获取助力券列表错误"
                            processRecorder?.recordProcess("获取助力券列表错误: $errorMsg", "ERROR")
                            Log.e("OrderViewModel", "Failed to get boost coupon list: $errorMsg")
                            showToast(errorMsg)
                            withContext(Dispatchers.Main) {
                                _boostCouponList.value = emptyList()
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取助力券列表异常: ${result.error.message}",
                            "ERROR"
                        )
                        Log.e("OrderViewModel", "Error fetching boost coupon list", result.error)
                        showToast("${result.error.message}")
                        withContext(Dispatchers.Main) {
                            _boostCouponList.value = emptyList()
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("获取助力券列表操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while fetching boost coupon list", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                    _boostCouponList.value = emptyList()
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 获取订单列表
     * 获取当前账号的订单列表
     * @param lastOrderId 最后一个订单ID，用于分页加载，首次加载传空字符串
     */
    fun getOrderList(lastOrderId: String = "") {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API获取订单列表
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.order.getOrderList(lastOrderId = lastOrderId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        // 使用ResponseParserUtils解析响应数据
                        val orderListResponse =
                            ResponseParserUtils.parseOrderListResponse(result.data)
                        if (orderListResponse != null && orderListResponse.code == 0) {
                            // 获取订单列表数据
                            val orders = orderListResponse.data?.orders ?: emptyList()
                            val hasNextPage = orderListResponse.data?.hasnextpage == 1
                            val newLastOrderId = orderListResponse.data?.lastorderid ?: ""

                            withContext(Dispatchers.Main) {
                                // 如果是加载更多（lastOrderId不为空），则将新数据添加到现有列表末尾
                                if (lastOrderId.isNotEmpty()) {
                                    _orderList.value += orders
                                } else {
                                    // 否则，替换现有列表
                                    _orderList.value = orders
                                }

                                // 计算订单状态统计信息
                                val orderListInfo = _orderList.value.groupBy { it.statusmsg }
                                    .map { "${it.key}${it.value.size}" }.joinToString("")
                                if (orderListInfo.isNotEmpty()) {
                                    processRecorder?.recordProcess("订单列表信息: $orderListInfo")
                                }
                                Log.d("OrderViewModel", "Order list info: $orderListInfo")

                                // 更新订单状态统计信息
                                if (orderListInfo.isNotEmpty() && !orderListInfo.startsWith("订单")) {
                                    _orderListInfo.value = "订单$orderListInfo"
                                } else {
                                    _orderListInfo.value = orderListInfo
                                }

                                _hasNextPage.value = hasNextPage
                                _lastOrderId.value = newLastOrderId

                                //processRecorder?.recordProcess("订单列表: 共${orders.size}个")
                                Log.d(
                                    "OrderViewModel",
                                    "Successfully fetched orders: ${orders.size}, hasNextPage: $hasNextPage, lastOrderId: $newLastOrderId"
                                )

                                /*if (orders.isEmpty() && lastOrderId.isEmpty()) {
                                    showToast("订单记录为空")
                                }*/
                            }
                        } else {
                            val errorMsg = orderListResponse?.message ?: "获取订单列表错误"
                            processRecorder?.recordProcess("获取订单列表错误: $errorMsg", "ERROR")
                            Log.e("OrderViewModel", "Failed to get order list: $errorMsg")
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取订单列表异常: ${result.error.message}",
                            "ERROR"
                        )
                        Log.e("OrderViewModel", "Error fetching order list", result.error)
                        showToast("获取订单列表异常: ${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("获取订单列表操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while fetching order list", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 获取邀请奖励列表
     * 获取当前账号所有邀请获得的奖励列表
     */
    fun getInvitationV2TotalRewardList(pageIndex: Int = 1, pageSize: Int = 10) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API获取邀请奖励列表
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.invitation.totalRewardList(pageIndex, pageSize)
                }

                when (result) {
                    is RequestResult.Success -> {
                        // 使用ResponseParserUtils解析响应数据
                        val invitationRewardResponse =
                            ResponseParserUtils.parseInvitationRewardResponse(result.data)
                        if (invitationRewardResponse != null && invitationRewardResponse.code == 0) {
                            // 更新状态
                            val rewards = invitationRewardResponse.data

                            withContext(Dispatchers.Main) {
                                _invitationRewards.value = rewards

                                processRecorder?.recordProcess("邀请奖励列表: 共${rewards.size}个")
                                Log.d(
                                    "OrderViewModel",
                                    "Successfully fetched invitation rewards: ${rewards.size}"
                                )
                            }
                        } else {
                            val errorMsg =
                                invitationRewardResponse?.message ?: "获取邀请奖励列表错误"
                            processRecorder?.recordProcess(
                                "获取邀请奖励列表错误: $errorMsg",
                                "ERROR"
                            )
                            Log.e("OrderViewModel", "Failed to get invitation rewards: $errorMsg")
                            showToast(errorMsg)
                            withContext(Dispatchers.Main) {
                                _invitationRewards.value = emptyList()
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取邀请奖励列表异常: ${result.error.message}",
                            "ERROR"
                        )
                        Log.e("OrderViewModel", "Error fetching invitation rewards", result.error)
                        showToast("获取邀请奖励列表异常: ${result.error.message}")
                        withContext(Dispatchers.Main) {
                            _invitationRewards.value = emptyList()
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("获取邀请奖励列表操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while fetching invitation rewards", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                    _invitationRewards.value = emptyList()
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 获取成功邀请列表
     * 获取当前账号成功邀请的用户列表
     * @param page 页码，从1开始
     * @param pageSize 每页数量，默认10
     */
    fun getInvitationV2SuccessList(page: Int = 1, pageSize: Int = 10) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API获取成功邀请列表
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.invitation.successInviteList(page, pageSize)
                }

                when (result) {
                    is RequestResult.Success -> {
                        // 使用ResponseParserUtils解析响应数据
                        val successInviteListResponse =
                            ResponseParserUtils.parseSuccessInviteListResponse(result.data)
                        if (successInviteListResponse != null && successInviteListResponse.code == 0) {
                            // 更新状态
                            val invites = successInviteListResponse.data
                            val pageCount = if (invites.isNotEmpty()) invites[0].pageCount else 0
                            val count = if (invites.isNotEmpty()) invites[0].count else 0

                            withContext(Dispatchers.Main) {
                                if (page == 1) {
                                    // 如果是第一页，则替换现有列表
                                    _successInvites.value = invites
                                } else {
                                    // 否则，将新数据添加到现有列表末尾
                                    _successInvites.value += invites
                                }

                                processRecorder?.recordProcess("成功邀请列表: 共${invites.size}个")
                                Log.d(
                                    "OrderViewModel",
                                    "Successfully fetched success invites: ${invites.size}, page: $page, total: $count, pageCount: $pageCount"
                                )

                                if (invites.isEmpty() && page == 1) {
                                    showToast("邀请记录为空")
                                }
                            }
                        } else {
                            val errorMsg =
                                successInviteListResponse?.message ?: "获取成功邀请列表错误"
                            processRecorder?.recordProcess(
                                "获取成功邀请列表错误: $errorMsg",
                                "ERROR"
                            )
                            Log.e("OrderViewModel", "Failed to get success invites: $errorMsg")
                            showToast(errorMsg)
                            if (page == 1) {
                                withContext(Dispatchers.Main) {
                                    _successInvites.value = emptyList()
                                }
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取成功邀请列表异常: ${result.error.message}",
                            "ERROR"
                        )
                        Log.e("OrderViewModel", "Error fetching success invites", result.error)
                        showToast("获取成功邀请列表异常: ${result.error.message}")
                        if (page == 1) {
                            withContext(Dispatchers.Main) {
                                _successInvites.value = emptyList()
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("获取成功邀请列表操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while fetching success invites", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                    if (page == 1) {
                        _successInvites.value = emptyList()
                    }
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 获取积分详情
     * @param page 页码，默认从0开始
     */
    fun getCreditDetail(page: Int = 0) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            try {
                // 调用API获取积分详情
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.credit.creditDetail(page)
                }

                when (result) {
                    is RequestResult.Success -> {
                        // 使用ResponseParserUtils解析响应数据
                        val creditResponse = ResponseParserUtils.parseCreditResponse(result.data)
                        if (creditResponse != null && creditResponse.code == 0 && creditResponse.data != null) {
                            val data = creditResponse.data
                            withContext(Dispatchers.Main) {
                                _creditData.value = data

                                if (page == 0) {
                                    // 如果是第一页，则替换现有列表
                                    _creditDetails.value = data.details ?: emptyList()
                                } else {
                                    // 否则，将新数据添加到现有列表末尾
                                    _creditDetails.value += data.details ?: emptyList()
                                }

                                _creditCount.intValue = data.count

                                processRecorder?.recordProcess("积分: ${data.credit}")
                                Log.d(
                                    "OrderViewModel",
                                    "Successfully fetched credit details: ${data.details?.size ?: 0}, page: $page, total: ${data.count}, pageCount: ${data.pagecount}"
                                )

                                if ((data.details?.isEmpty() != false) && page == 0) {
                                    processRecorder?.recordProcess("积分: 积分记录为空", "WARNING")
                                    showToast("积分记录为空")
                                }
                            }
                        } else {
                            val errorMsg = creditResponse?.message ?: "获取积分错误"
                            processRecorder?.recordProcess("获取积分错误: $errorMsg", "ERROR")
                            Log.e("OrderViewModel", "Failed to get credit details: $errorMsg")
                            showToast(errorMsg)
                            if (page == 0) {
                                withContext(Dispatchers.Main) {
                                    _creditData.value = null
                                    _creditDetails.value = emptyList()
                                    _creditCount.intValue = 0
                                }
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取积分异常: ${result.error.message}",
                            "ERROR"
                        )
                        Log.e("OrderViewModel", "Error fetching credit details", result.error)
                        showToast("获取积分异常: ${result.error.message}")
                        if (page == 0) {
                            withContext(Dispatchers.Main) {
                                _creditData.value = null
                                _creditDetails.value = emptyList()
                                _creditCount.intValue = 0
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("获取积分操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while fetching credit details", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                    if (page == 0) {
                        _creditData.value = null
                        _creditDetails.value = emptyList()
                        _creditCount.intValue = 0
                    }
                }
            }
        }
    }

    /**
     * 取消赠送永辉卡
     * @param card 卡
     */
    fun cancelSendCard(card: CardItem) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.card.cancelSendCard(card.cardNo)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response = ResponseParserUtils.parseCancelSendCardResponse(result.data)
                        if (response != null && response.code == 0) {
                            withContext(Dispatchers.Main) {
                                processRecorder?.recordProcess("取消赠送成功")
                                showToast("取消赠送成功")
                            }
                            // 刷新卡列表
                            getAllCard()
                        } else {
                            processRecorder?.recordProcess(
                                "取消赠送错误: ${response?.message}",
                                "ERROR"
                            )
                            showToast("${response?.message}")
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "取消赠送异常: ${result.error.message}",
                            "ERROR"
                        )
                        Log.e("OrderViewModel", "Error canceling send card", result.error)
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("取消赠送操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while canceling send card", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 赠送永辉卡
     * @param card 卡
     */
    fun sendCard(card: CardItem) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.card.sendCard(
                        card.cardNo,
                        card.cardAmount,
                        "1",
                        card.coverId,
                        card.giveType
                    )
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response = ResponseParserUtils.parseSendCardResponse(result.data)
                        if (response != null && response.code == 0) {
                            withContext(Dispatchers.Main) {
                                val clipBoardText =
                                    "${_currentSelectedAccount.value?.phoneNumber},${response.data?.yhPackageId},${card.cardBalance}"
                                copyToClipboard("赠送信息", clipBoardText)
                                processRecorder?.recordProcess("赠送成功: $clipBoardText")
                            }
                            // 刷新卡列表
                            getAllCard()
                        } else {
                            processRecorder?.recordProcess(
                                "赠送卡错误: ${response?.message}",
                                "ERROR"
                            )
                            showToast("${response?.message}")
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "赠送卡异常: ${result.error.message}",
                            "ERROR"
                        )
                        Log.e("OrderViewModel", "Error sending card", result.error)
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("赠送卡操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while sending card", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 简单赠送永辉卡
     * @param card 卡
     */
    fun simpleSendCard(card: CardItem) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.card.getCardSimplePresent(
                        card.cardNo,
                        card.cardAmount,
                        "1",
                        card.coverId
                    )
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response = ResponseParserUtils.parseSendCardSimpleResponse(result.data)
                        if (response != null && response.code == 0) {
                            withContext(Dispatchers.Main) {
                                val clipBoardText =
                                    "${_currentSelectedAccount.value?.phoneNumber},${response.data?.yhPackageId},${card.cardBalance}"
                                copyToClipboard("赠送信息", clipBoardText)
                                processRecorder?.recordProcess("简单赠送成功：$clipBoardText")
                            }
                        } else {
                            processRecorder?.recordProcess(
                                "简单赠送卡错误: ${response?.message}",
                                "ERROR"
                            )
                            showToast("${response?.message}")
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "简单赠送卡异常: ${result.error.message}",
                            "ERROR"
                        )
                        Log.e("OrderViewModel", "Error sending card", result.error)
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("简单赠送卡操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while sending card", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    // --- AccountDialog 相关函数 End ---

    /**
     * 同步账号相关数据 余额、积分、订单
     */
    private fun syncAccountSomeData() {
        getUserInfo()
        checkActivity()
        checkCardBuy()
        getAllCard()
        getCardIndexInfo()
        getOrderList()
        getCreditDetail()
    }

    /**
     * 检查账号类型 (内部实现)
     * @return 是否需要登录
     */
    private suspend fun checkAccountType(setAccountDisplayText: (String) -> Unit): Boolean {
        val currentRequestService = requestService
        if (currentRequestService == null) {
            showToast("服务未初始化")
            return true
        }

        try {
            // 调用API获取账户类型信息
            val result = withContext(Dispatchers.IO) {
                currentRequestService.user.checkAccountType()
            }

            return when (result) {
                is RequestResult.Success -> {
                    // 使用ResponseParserUtils解析响应数据
                    val homepageResponse =
                        ResponseParserUtils.parseHomepageResponse(result.data)

                    if (homepageResponse != null && homepageResponse.code == 0) {
                        // 检查是否需要登录
                        val needLogin =
                            homepageResponse.data?.loginPopVO?.newPersonLogin == "200"

                        // 检查账户类型
                        if (!needLogin) {
                            val accountType = determineAccountType(homepageResponse)
                            _accountType.value = accountType

                            processRecorder?.recordProcess("账户类型: $accountType")
                            Log.d(
                                "OrderViewModel",
                                "Account type determined: $accountType"
                            )
                        }

                        withContext(Dispatchers.Main) {
                            _needLogin.value = needLogin

                            processRecorder?.recordProcess("账户有效性: ${if (needLogin) "已失效" else "有效"}")
                            Log.d(
                                "OrderViewModel",
                                "Successfully checked account type: needLogin=$needLogin"
                            )
                        }

                        needLogin
                    } else {
                        val errorMsg = homepageResponse?.message ?: "账户类型未知"
                        processRecorder?.recordProcess("账户有效性检查结果: $errorMsg")
                        Log.e("OrderViewModel", "Failed to check account type: $errorMsg")
                        setAccountDisplayText(errorMsg)
                        showToast(errorMsg)
                        true
                    }
                }

                is RequestResult.Error -> {
                    processRecorder?.recordProcess(
                        "账户类型检查异常: ${result.error.message}",
                        "ERROR"
                    )
                    Log.e("OrderViewModel", "Error checking account type", result.error)
                    showToast("${result.error.message}")
                    setAccountDisplayText(result.error.message ?: "发生未知错误")
                    true
                }
            }
        } catch (e: Exception) {
            processRecorder?.recordProcess("账户类型检查操作异常: ${e.message}", "ERROR")
            Log.e("OrderViewModel", "Exception while checking account type", e)
            showToast("${e.message}")
            return true
        }
    }

    fun getUserInfo() {
        viewModelScope.launch {
            setLoadingTokenInfo(true)
            try {
                val result = withContext(Dispatchers.IO) {
                    requestService?.user?.getUserInfo()
                }

                when (result) {
                    is RequestResult.Success -> {
                        // 使用ResponseParserUtils解析响应数据
                        val accountResponse = ResponseParserUtils.parseUserInfoResponse(result.data)
                        if (accountResponse != null && accountResponse.code == 0 && accountResponse.data != null) {
                            val data = accountResponse.data
                            withContext(Dispatchers.Main) {
                                // 设置账户显示文本
                                val displayText = data.nickname + " " + data.signupTime

                                setAccountDisplayText(displayText)

                                processRecorder?.recordProcess("账户信息: $displayText")
                                Log.d(
                                    "OrderViewModel",
                                    "Successfully fetched account info: ${data.nickname}, id: ${data.memberid}"
                                )
                            }
                        } else {
                            val errorMsg = accountResponse?.message ?: "获取账户信息错误"
                            processRecorder?.recordProcess("获取账户信息错误: $errorMsg", "ERROR")
                            Log.e("OrderViewModel", "Failed to get account info: $errorMsg")
                            showToast(errorMsg)
                            withContext(Dispatchers.Main) {
                                setAccountDisplayText("账号未设定")
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取账户信息异常: ${result.error.message}",
                            "ERROR"
                        )
                        Log.e("OrderViewModel", "Error fetching account info", result.error)
                        showToast("${result.error.message}")
                        withContext(Dispatchers.Main) {
                            setAccountDisplayText("账号未设定")
                        }
                    }

                    else -> {
                        processRecorder?.recordProcess("获取账户信息错误: 未知结果类型", "ERROR")
                        Log.e("OrderViewModel", "Unknown result type when fetching account info")
                        showToast("获取账户信息错误")
                        withContext(Dispatchers.Main) {
                            setAccountDisplayText("账号未设定")
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("获取账户信息操作异常: ${e.message}", "ERROR")
                Log.e("OrderViewModel", "Exception while fetching account info", e)
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                    setAccountDisplayText("账号未设定")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    fun checkActivity() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API检查活动状态
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.user.checkActivity()
                }

                when (result) {
                    is RequestResult.Success -> {
                        try {
                            // 解析响应数据
                            val responseJson = JSONObject(result.data)
                            val checkActivityStr = when {
                                // 判断响应码属于哪种状态
                                responseJson.optInt("code") == 410008 ||
                                        responseJson.optInt("code") == 22001 ||
                                        responseJson.optInt("code") == 20022 -> "白"

                                responseJson.optInt("code") == 22002 -> "黑"

                                else -> "未知"
                            }

                            withContext(Dispatchers.Main) {
                                // 更新活动检查状态
                                _checkActivityStr.value = checkActivityStr
                                processRecorder?.recordProcess("活动状态: $checkActivityStr")
                                Log.d(
                                    "OrderViewModel",
                                    "Activity check result: $checkActivityStr, code: ${
                                        responseJson.optInt("code")
                                    }"
                                )
                            }
                        } catch (e: Exception) {
                            Log.e("OrderViewModel", "Failed to parse activity check response", e)
                            processRecorder?.recordProcess(
                                "活动检查解析错误: ${e.message}",
                                "ERROR"
                            )
                            withContext(Dispatchers.Main) {
                                _checkActivityStr.value = "黑"
                                showToast(" ${e.message}")
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "Error checking activity", result.error)
                        processRecorder?.recordProcess(
                            "活动检查异常: ${result.error.message}",
                            "ERROR"
                        )
                        withContext(Dispatchers.Main) {
                            _checkActivityStr.value = ""
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while checking activity", e)
                processRecorder?.recordProcess("活动检查操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    _checkActivityStr.value = ""
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    fun checkCardBuy() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API检查卡片购买状态
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.card.checkCardBuy()
                }

                when (result) {
                    is RequestResult.Success -> {
                        try {
                            // 使用ResponseParserUtils解析响应数据
                            val response = ResponseParserUtils.parseCardPlaceResponse(result.data)
                            if (response != null && response.code == 0) {
                                // 如果响应成功且code为0，设置为"白"
                                withContext(Dispatchers.Main) {
                                    _checkCardBuyStr.value = "白"
                                    processRecorder?.recordProcess("购买状态: 白")
                                    Log.d("OrderViewModel", "Card buy check result: 白, code: 0")
                                }
                            } else {
                                // 其他情况设置为黑
                                withContext(Dispatchers.Main) {
                                    _checkCardBuyStr.value = "黑"
                                    processRecorder?.recordProcess(
                                        "购买状态: 黑",
                                        "WARNING"
                                    )
                                    Log.d(
                                        "OrderViewModel",
                                        "Card buy check failed: ${response?.message}"
                                    )
                                }
                            }
                        } catch (e: Exception) {
                            Log.e("OrderViewModel", "Failed to parse card buy check response", e)
                            processRecorder?.recordProcess(
                                "检查卡片购买状态解析错误: ${e.message}",
                                "ERROR"
                            )
                            withContext(Dispatchers.Main) {
                                _checkCardBuyStr.value = ""
                                showToast("${e.message}")
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "Error checking card buy", result.error)
                        processRecorder?.recordProcess(
                            "检查卡片购买状态异常: ${result.error.message}",
                            "ERROR"
                        )
                        withContext(Dispatchers.Main) {
                            _checkCardBuyStr.value = ""
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while checking card buy", e)
                processRecorder?.recordProcess("检查卡片购买状态异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    _checkCardBuyStr.value = ""
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置是否显示代理选择对话框
     */
    fun setShowProxySelectDialog(show: Boolean) {
        _showProxySelectDialog.value = show
    }

    /**
     * 设置选中的代理
     */
    fun setSelectedProxy(proxy: String) {
        _selectedProxy.value = proxy
        // 如果选中了代理，则启用代理
        if (proxy.isNotEmpty()) {
            _enableProxy.value = true
        }
    }

    /**
     * 设置是否启用代理
     */
    fun setEnableProxy(enable: Boolean) {
        _enableProxy.value = enable
        // 如果禁用代理，则清空选中的代理，并清除代理
        if (!enable) {
            viewModelScope.launch {
                _selectedProxy.value = ""
                HttpProxyUtils.clearProxy()
                requestService?.updateClient()
            }
        }
    }

    /**
     * 设置是否显示绑定永辉卡对话框
     */
    fun setShowBindCardDialog(show: Boolean) {
        _showBindCardDialog.value = show
    }

    /**
     * 验证永辉卡
     * @param cardInfo 卡信息
     * @param callback 回调函数，返回卡余额
     */
    fun validateCardOrLink(cardInfo: String, callback: (String) -> Unit) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                val (cardNo, password) = if (cardInfo.startsWith("http")) {
                    listOf(cardInfo, "")
                } else {
                    // 分割卡号和密码
                    val parts = cardInfo.split(",")
                    listOf(parts[0], parts[1])
                }

                // 调用验证卡接口
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.card.validateCardOrLink(cardNo, password)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseValidateCardOrLinkResponse(result.data)
                        if (response?.code == 0) {
                            val balance = response.data?.balance ?: "0"
                            processRecorder?.recordProcess("验证成功: $cardInfo，余额: $balance")
                            withContext(Dispatchers.Main) {
                                callback(balance.toString())
                                showToast("验证成功，余额: $balance")
                            }
                        } else {
                            processRecorder?.recordProcess(
                                "验证错误: $cardInfo, ${response?.message}",
                                "ERROR"
                            )
                            withContext(Dispatchers.Main) {
                                showToast("${response?.message}")
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "验证异常: $cardInfo, ${result.error.message}",
                            "ERROR"
                        )
                        withContext(Dispatchers.Main) {
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while validating card", e)
                processRecorder?.recordProcess("验证操作异常: $cardInfo, ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 绑定永辉卡
     * @param cardInfo 卡信息
     */
    fun bindCardOrLink(cardInfo: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 解析卡信息
                val (cardNo, password) = if (cardInfo.startsWith("http")) {
                    listOf(cardInfo, "")
                } else {
                    // 分割卡号和密码
                    val parts = cardInfo.split(",")
                    listOf(parts[0], parts[1])
                }

                // 调用绑定卡接口
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.card.bindCardOrLink(cardNo, password)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response = ResponseParserUtils.parseBindCardOrLinkResponse(result.data)
                        withContext(Dispatchers.Main) {
                            if (response?.code == 0) {
                                processRecorder?.recordProcess("绑定成功: $cardInfo")
                                showToast("绑定成功")
                                // 刷新卡列表
                                getAllCard()
                                // 刷新永辉卡余额
                                getCardIndexInfo()
                            } else {
                                processRecorder?.recordProcess(
                                    "绑定错误: $cardInfo, ${response?.message}",
                                    "ERROR"
                                )
                                showToast("${response?.message}")
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "绑定异常: $cardInfo, ${result.error.message}",
                            "ERROR"
                        )
                        withContext(Dispatchers.Main) {
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while binding card", e)
                processRecorder?.recordProcess("绑定操作异常: $cardInfo, ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 领取永辉卡
     * @param cardInfo 卡信息，格式：phoneNumber,yhPackageId,balance
     */
    fun receiveCard(cardInfo: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 分割卡信息
                val parts = cardInfo.split(",")
                if (parts.size < 3) {
                    showToast("格式不正确")
                    processRecorder?.recordProcess("格式不正确: $cardInfo", "ERROR")
                    return@launch
                }

                val yhPackageId = parts[1]

                // 调用领取卡接口
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.card.receiveCard(yhPackageId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response = ResponseParserUtils.parseReceiveCardResponse(result.data)
                        withContext(Dispatchers.Main) {
                            if (response?.code == 0) {
                                val receiveCardData = response.data
                                processRecorder?.recordProcess("领取成功: $cardInfo，风险等级: ${receiveCardData?.riskLevel}")
                                showToast("领取成功，风险等级: ${receiveCardData?.riskLevel}")
                                // 刷新卡列表
                                getAllCard()
                                // 刷新永辉卡余额
                                getCardIndexInfo()
                            } else {
                                processRecorder?.recordProcess(
                                    "领取错误: $cardInfo, ${response?.message}",
                                    "ERROR"
                                )
                                showToast("${response?.message}")
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "领取异常: $cardInfo, ${result.error.message}",
                            "ERROR"
                        )
                        withContext(Dispatchers.Main) {
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while receiving card", e)
                processRecorder?.recordProcess("领取操作异常: $cardInfo, ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置是否显示新人优惠券对话框
     */
    fun setShowNewPersonCouponDialog(show: Boolean) {
        _showNewPersonCouponDialog.value = show
    }

    /**
     * 设置是否显示新人券参数快速设置对话框
     */
    fun setShowNewPersonCouponShopParamsFastSetDialog(show: Boolean) {
        _showNewPersonCouponShopParamsFastSetDialog.value = show
    }

    /**
     * 设置新人优惠券店铺参数
     */
    fun setNewPersonCouponShopParams(params: String) {
        _newPersonCouponShopParams.value = params
    }

    /**
     * 设置活动规则
     */
    fun setCommonActivityRule(rule: String) {
        _commonActivityRule.value = rule
    }

    /**
     * 设置是否显示活动规则对话框
     */
    fun setShowCommonActivityRuleDialog(show: Boolean) {
        _showCommonActivityRuleDialog.value = show
        if (!show) {
            _commonActivityRule.value = ""
        }
    }

    /**
     * 获取预设店铺参数列表
     * @return 预设店铺参数列表
     */
    fun getPreConfigShopParams(): List<JSONObject> {
        try {
            val preConfigList = mutableListOf<JSONObject>()

            val preConfig1 = JSONObject()
            preConfig1.put("preName", "福州 9M7W")
            preConfig1.put("newPersonPopupShopParams", "7,9M7W")

            val preConfig2 = JSONObject()
            preConfig2.put("preName", "四川 9348")
            preConfig2.put("newPersonPopupShopParams", "7,9348")

            val preConfig3 = JSONObject()
            preConfig3.put("preName", "上海 90N3")
            preConfig3.put("newPersonPopupShopParams", "7,90N3")

            preConfigList.add(preConfig1)
            preConfigList.add(preConfig2)
            preConfigList.add(preConfig3)

            return preConfigList
        } catch (e: Exception) {
            Log.e("OrderViewModel", "Error creating preConfig shop params list", e)
            return emptyList()
        }
    }

    /**
     * 保存新人优惠券店铺参数
     */
    fun saveNewPersonCouponShopParams(shopParams: String) {
        try {
            setNewPersonCouponShopParams(shopParams)
            processRecorder?.recordProcess("店铺参数保存成功: $shopParams")
            Log.d(
                "OrderViewModel",
                "New person coupon shop params saved: ${_newPersonCouponShopParams.value}"
            )
            showToast("账号配置保存成功")
        } catch (e: Exception) {
            processRecorder?.recordProcess("店铺参数保存错误: ${e.message}", "ERROR")
            Log.e("OrderViewModel", "Error saving new person coupon shop params", e)
            showToast("${e.message}")
        }
    }

    /**
     * 获取当前地址新人优惠券信息（活动规则）
     */
    fun getNewPersonCouponInfo(sellerId: String, shopId: String, callback: (String) -> Unit) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API获取新人优惠券信息
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.coupon.getNewPersonCouponInfo(sellerId, shopId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseNewPersonCouponInfoResponse(result.data)
                        withContext(Dispatchers.Main) {
                            if (response?.code == 0) {
                                // 提取活动规则 
                                val couponVos = response.data?.couponResult?.couponVos
                                val activityRule =
                                    couponVos?.mapIndexed { index, it -> "${index + 1}、${it.name} ${it.couponDescription} ${it.applicationScope}" }
                                        ?.joinToString("\n") ?: ""
                                if (activityRule.isNotEmpty()) {
                                    callback(activityRule)
                                } else {
                                    showToast("解析活动规则错误")
                                }
                                processRecorder?.recordProcess("新人优惠券信息: $activityRule")
                                Log.d(
                                    "OrderViewModel",
                                    "Successfully fetched new person coupon info"
                                )
                            } else {
                                processRecorder?.recordProcess(
                                    "获取新人优惠券信息错误: $sellerId, $shopId, ${response?.message}",
                                    "ERROR"
                                )
                                Log.e(
                                    "OrderViewModel",
                                    "Failed to fetch new person coupon info: ${response?.message}"
                                )
                                showToast("${response?.message}")
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取新人优惠券信息异常: ${result.error.message}",
                            "ERROR"
                        )
                        withContext(Dispatchers.Main) {
                            Log.e(
                                "OrderViewModel",
                                "Failed to fetch new person coupon info: ${result.error.message}"
                            )
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("获取新人优惠券信息操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    Log.e(
                        "OrderViewModel",
                        "Exception while fetching new person coupon info: ${e.message}"
                    )
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 领取新人优惠券
     */
    fun getNewPersonCoupon(sellerId: String, shopId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API领取新人优惠券
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.coupon.getNewPersonPopup(sellerId, shopId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response = ResponseParserUtils.parseNewPersonPopupResponse(result.data)
                        withContext(Dispatchers.Main) {
                            if (response?.code == 0) {
                                val totalAmount = response.data?.totalAmount
                                if (totalAmount != null && totalAmount != 0) {
                                    processRecorder?.recordProcess("领取新人优惠券成功: $sellerId, $shopId, 金额: ${totalAmount / 100}")
                                    showToast("领取成功/$totalAmount")
                                    // 刷新优惠券列表
                                    getCouponList()
                                } else {
                                    processRecorder?.recordProcess("领取新人优惠券错误: $sellerId, $shopId, 已领券/黑号/老号")
                                    showToast("已领券/黑号/老号")
                                }
                            } else {
                                processRecorder?.recordProcess(
                                    "领取新人优惠券错误: $sellerId, $shopId, ${response?.message}",
                                    "ERROR"
                                )
                                Log.e(
                                    "OrderViewModel",
                                    "Failed to get new person popup: ${response?.message}"
                                )
                                showToast("${response?.message}")
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "领取新人优惠券异常: ${result.error.message}",
                            "ERROR"
                        )
                        withContext(Dispatchers.Main) {
                            Log.e(
                                "OrderViewModel",
                                "Failed to get new person popup: ${result.error.message}"
                            )
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("领取新人优惠券操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    Log.e("OrderViewModel", "Exception while getting new person coupon", e)
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置是否显示领取优惠券对话框
     */
    fun setShowKindCouponDialog(show: Boolean) {
        _showKindCouponDialog.value = show
    }

    /**
     * 设置优惠券参数
     */
    fun setCouponPromotionCodeStr(code: String) {
        _couponPromotionCodeStr.value = code
    }

    /**
     * 获取预配置优惠券参数列表
     * @return 预配置优惠券参数列表
     */
    fun getPreConfigKindCouponList(): List<JSONObject> {
        // 这里应该从数据库或配置文件中加载预设优惠券参数
        try {
            val preConfigList = mutableListOf<JSONObject>()

            /* val preConfig2 = JSONObject()
             preConfig2.put("preName", "重庆 8.8折券")
             preConfig2.put("couponPromotionCodeStr", "HX2X-HX-25N-88Z-5;")
 
             preConfigList.add(preConfig2)*/

            return preConfigList
        } catch (e: Exception) {
            Log.e("OrderViewModel", "Error creating preConfig kind coupon list", e)
            return emptyList()
        }
    }

    /**
     * 保存优惠券参数
     */
    fun saveCouponPromotionCode(code: String) {
        try {
            setCouponPromotionCodeStr(code)
            Log.d(
                "OrderViewModel",
                "Coupon promotion code saved: ${_couponPromotionCodeStr.value}"
            )
            showToast("账号配置保存成功")
        } catch (e: Exception) {
            Log.e("OrderViewModel", "Error saving coupon promotion code", e)
            showToast("${e.message}")
        }
    }

    /**
     * 获取社群新人券信息
     */
    fun getNewUserCouponCode(callback: (String) -> Unit) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API获取社群新人券信息
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.coupon.getNewUserCouponInfo()
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseNewUserCouponInfoResponse(result.data)
                        withContext(Dispatchers.Main) {
                            if (response?.code == 0) {
                                // 提取社群新人券码
                                val code = response.data?.code ?: ""
                                if (code.isNotEmpty()) {
                                    // 设置社群新人券码
                                    callback("GROUP_NEW_USER_COUPON," + code)
                                    showToast("社群新人券参数获取成功")
                                } else {
                                    showToast("未获取到社群新人券参数")
                                }
                                processRecorder?.recordProcess("获取社群新人券信息结果: $code")
                                Log.d(
                                    "OrderViewModel",
                                    "Successfully fetched new user coupon info: $code"
                                )
                            } else {
                                processRecorder?.recordProcess(
                                    "获取社群新人券信息错误: ${response?.message}",
                                    "ERROR"
                                )
                                Log.e(
                                    "OrderViewModel",
                                    "Failed to fetch new user coupon info: ${response?.message}"
                                )
                                showToast("${response?.message}")
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "获取社群新人券信息异常: ${result.error.message}",
                            "ERROR"
                        )
                        withContext(Dispatchers.Main) {
                            Log.e(
                                "OrderViewModel",
                                "Failed to fetch new user coupon info: ${result.error.message}"
                            )
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("获取社群新人券信息操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    Log.e(
                        "OrderViewModel",
                        "Exception while fetching new user coupon info: ${e.message}"
                    )
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 领取社群新人券
     */
    fun getNewUserCouponReceive(activityCode: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API领取社群新人券
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.coupon.getNewUserGroupCoupon(activityCode)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseNewUserCouponReceiveResponse(result.data)
                        withContext(Dispatchers.Main) {
                            if (response?.code == 0) {
                                processRecorder?.recordProcess("领取社群新人券成功: $activityCode")
                                showToast("领取社群新人券成功")
                                // 刷新优惠券列表
                                getCouponList()
                            } else {
                                processRecorder?.recordProcess(
                                    "领取社群新人券错误: $activityCode, ${response?.message}",
                                    "ERROR"
                                )
                                Log.e(
                                    "OrderViewModel",
                                    "Failed to receive new user coupon: ${response?.message}"
                                )
                                showToast("${response?.message}")
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "领取社群新人券异常: ${result.error.message}",
                            "ERROR"
                        )
                        withContext(Dispatchers.Main) {
                            Log.e(
                                "OrderViewModel",
                                "Failed to receive new user coupon: ${result.error.message}"
                            )
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("领取社群新人券操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    Log.e("OrderViewModel", "Exception while receiving new user coupon", e)
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 领取多种优惠券
     */
    fun kindCoupon(couponCode: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API领取优惠券
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.coupon.kindCoupon(couponCode)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response = ResponseParserUtils.parseKindCouponResponse(result.data)
                        withContext(Dispatchers.Main) {
                            if (response?.code == 0) {
                                processRecorder?.recordProcess("领取优惠券成功: $couponCode")
                                showToast("优惠券领取成功")
                                // 刷新优惠券列表
                                getCouponList()
                            } else {
                                processRecorder?.recordProcess(
                                    "领取优惠券错误: $couponCode, ${response?.message}",
                                    "ERROR"
                                )
                                Log.e(
                                    "OrderViewModel",
                                    "Failed to receive kind coupon: ${response?.message}"
                                )
                                showToast("${response?.message}")
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        processRecorder?.recordProcess(
                            "领取优惠券异常: ${result.error.message}",
                            "ERROR"
                        )
                        withContext(Dispatchers.Main) {
                            Log.e(
                                "OrderViewModel",
                                "Failed to receive kind coupon: ${result.error.message}"
                            )
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                processRecorder?.recordProcess("领取优惠券操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    Log.e("OrderViewModel", "Exception while receiving kind coupon", e)
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置是否显示拼手气对话框
     */
    fun setShowHandLuckDialog(show: Boolean) {
        _showHandLuckDialog.value = show
    }

    /**
     * 设置拼手气活动ID
     */
    fun setHandLuckEventIdStr(eventId: String) {
        _handLuckEventIdStr.value = eventId
    }

    /**
     * 获取拼手气相关数据并解析
     * @param eventId 事件ID
     */
    fun handLuckGetSlotMachineData(eventId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API获取拼手气相关数据
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.handLuck.handLuckGetSlotMachineData(eventId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        // 解析响应数据
                        val response =
                            ResponseParserUtils.parseHandLuckSlotMachineDataResponse(result.data)
                        if (response != null && response.code == 0) {
                            // 获取活动规则
                            val rule = response.data?.activityRule ?: ""
                            if (rule.isNotEmpty()) {
                                withContext(Dispatchers.Main) {
                                    setCommonActivityRule(rule)
                                    setShowCommonActivityRuleDialog(true)
                                    processRecorder?.recordProcess("获取拼手气活动规则成功: $rule")
                                    Log.d(
                                        "OrderViewModel",
                                        "Successfully fetched hand luck activity rule"
                                    )
                                }
                            } else {
                                processRecorder?.recordProcess("获取拼手气活动规则结果: 未获取到拼手气活动规则")
                                showToast("未获取到拼手气活动规则")
                            }
                        } else {
                            val errorMsg = response?.message ?: "拼手气活动规则载入错误"
                            Log.e(
                                "OrderViewModel",
                                "Failed to get hand luck activity rule: $errorMsg"
                            )
                            processRecorder?.recordProcess(
                                "获取拼手气活动规则错误: $eventId, $errorMsg",
                                "ERROR"
                            )
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e(
                            "OrderViewModel",
                            "Error fetching hand luck activity rule",
                            result.error
                        )
                        processRecorder?.recordProcess(
                            "获取拼手气活动规则异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while fetching hand luck activity rule", e)
                processRecorder?.recordProcess("获取拼手气活动规则操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置是否显示拼手气奖励列表对话框
     */
    fun setShowHandLuckRewardListDialog(show: Boolean) {
        _showHandLuckRewardListDialog.value = show
        if (!show) {
            _handLuckRewardList.value = emptyList()
        }
    }

    /**
     * 获取拼手气奖励列表
     */
    fun handLuckGetTotalRewardList(eventId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.handLuck.handLuckGetTotalRewardList(eventId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response = ResponseParserUtils.parseHandLuckTotalRewardListResponse(
                            result.data
                        )
                        if (response != null && response.code == 0) {
                            val rewardList = response.data
                            if (rewardList != null) {
                                _handLuckRewardList.value = rewardList
                                _showHandLuckRewardListDialog.value = true
                                processRecorder?.recordProcess("获取拼手气奖励列表成功: 共${rewardList.size}条")
                            } else {
                                processRecorder?.recordProcess(
                                    "获取拼手气奖励列表成功: 未获取到拼手气奖励列表",
                                    "WARNING"
                                )
                                showToast("未获取到拼手气奖励列表")
                            }
                        } else {
                            val errorMsg = response?.message ?: "拼手气奖励列表载入错误"
                            Log.e(
                                "OrderViewModel",
                                "Failed to get hand luck total reward list: $errorMsg"
                            )
                            processRecorder?.recordProcess(
                                "获取拼手气奖励列表错误: $eventId, $errorMsg",
                                "ERROR"
                            )
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e(
                            "OrderViewModel",
                            "Error fetching hand luck total reward list",
                            result.error
                        )
                        processRecorder?.recordProcess(
                            "获取拼手气奖励列表异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while fetching hand luck total reward list", e)
                processRecorder?.recordProcess("获取拼手气奖励列表操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 拼手气
     * @param eventId 活动ID
     */
    fun handLuckDrawLottery(eventId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            var activityCode: String
            try {
                // activityCode
                val activityCodeResult = withContext(Dispatchers.IO) {
                    currentRequestService.handLuck.handLuckGetSlotMachineData(eventId)
                }
                when (activityCodeResult) {
                    is RequestResult.Success -> {
                        // 解析响应数据
                        val response = ResponseParserUtils.parseHandLuckSlotMachineDataResponse(
                            activityCodeResult.data
                        )
                        if (response != null && response.code == 0) {
                            // activityCode
                            activityCode = response.data?.activityCode ?: ""
                            processRecorder?.recordProcess("获取拼手气活动码成功: $activityCode")
                            Log.d(
                                "OrderViewModel",
                                "Successfully fetched hand luck activity code: $activityCode"
                            )
                            val couponInfo = response.data?.redEnvelope?.couponInfo
                            if (couponInfo != null) {
                                val amount = couponInfo.amount
                                val showStr = "${amount}元 ${couponInfo.couponDescription}"
                                showToast(showStr)
                                setShowHandLuckDialog(false)
                                return@launch
                            }
                        } else {
                            val errorMsg = response?.message ?: "ActivityCode载入错误"
                            Log.e(
                                "OrderViewModel",
                                "Failed to get hand luck activity code: $errorMsg"
                            )
                            processRecorder?.recordProcess(
                                "获取拼手气活动码错误: $eventId, $errorMsg",
                                "ERROR"
                            )
                            showToast(errorMsg)
                            return@launch
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e(
                            "OrderViewModel",
                            "Error fetching hand luck activity rule",
                            activityCodeResult.error
                        )
                        processRecorder?.recordProcess(
                            "获取拼手气活动码异常: ${activityCodeResult.error.message}",
                            "ERROR"
                        )
                        showToast("${activityCodeResult.error.message}")
                        return@launch
                    }
                }

                if (activityCode.isEmpty()) {
                    showToast("活动码获取错误")
                    processRecorder?.recordProcess(
                        "获取拼手气活动码错误: $eventId, 活动码获取错误",
                        "ERROR"
                    )
                    return@launch
                }

                // 调用API拼手气
                val handLuckDrawLotteryResult = withContext(Dispatchers.IO) {
                    currentRequestService.handLuck.handLuckDrawLottery(eventId, activityCode)
                }

                when (handLuckDrawLotteryResult) {
                    is RequestResult.Success -> {
                        // 解析响应数据
                        val response = ResponseParserUtils.parseHandLuckDrawLotteryResponse(
                            handLuckDrawLotteryResult.data
                        )
                        if (response != null && response.code == 0) {
                            val couponInfo = response.data?.couponInfo
                            val amount = couponInfo?.amount ?: 0
                            val showStr = "${amount}元 ${couponInfo?.couponDescription}"
                            // 关闭拼手气对话框
                            setShowHandLuckDialog(false)
                            processRecorder?.recordProcess("拼手气成功: $eventId, 金额: $amount")
                            showToast(showStr)
                        } else {
                            val errorMsg = response?.message ?: "拼手气错误"
                            processRecorder?.recordProcess(
                                "拼手气错误: $eventId, $errorMsg",
                                "ERROR"
                            )
                            Log.e("OrderViewModel", "Failed to get hand luck data: $errorMsg")
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e(
                            "OrderViewModel",
                            "Error fetching hand luck data",
                            handLuckDrawLotteryResult.error
                        )
                        processRecorder?.recordProcess(
                            "拼手气异常: ${handLuckDrawLotteryResult.error.message}",
                            "ERROR"
                        )
                        showToast("${handLuckDrawLotteryResult.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while fetching hand luck data", e)
                processRecorder?.recordProcess("拼手气操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置是否显示助力券配置对话框
     */
    fun setShowBoostCouponConfigDialog(show: Boolean) {
        _showBoostCouponConfigDialog.value = show
    }

    /**
     * 设置是否显示抢券配置对话框
     */
    fun setShowCouponGrabbingConfigDialog(show: Boolean) {
        _showCouponGrabbingConfigDialog.value = show
    }

    /**
     * 保存助力券配置数据
     * @param helpDataStr 助力券配置数据字符串，格式为逗号分隔的ID列表
     */
    fun saveBoostCouponHelpData(helpDataStr: String) {
        val helpDataList = helpDataStr.split(",").map { it.trim() }.filter { it.isNotEmpty() }
        _boostCouponHelpData.value = helpDataList
        showToast("助力券配置 保存成功")
        processRecorder?.recordProcess("保存助力券配置数据成功: $helpDataList")
        Log.d("OrderViewModel", "Boost coupon help data saved: $helpDataList")
    }

    /**
     * 格式化并保存助力券配置数据
     */
    fun formatAndSaveBoostCouponHelpData() {
        // 创建一个可变列表来收集助力券ID
        val helpDataList = mutableListOf<String>()

        // 遍历助力券列表
        _boostCouponList.value.forEach { boostCoupon ->
            Log.d("OrderViewModel", "Boost coupon: $boostCoupon")
            val prizeGameDTO = boostCoupon.prizeGameDTO
            val prizeId = prizeGameDTO?.prizeId?.toString()
            val prizeGameStatus = prizeGameDTO?.prizeGameStatus

            if (prizeGameStatus != null && prizeGameStatus == 2 && prizeId != null) {
                Log.d("OrderViewModel", "Boost coupon prizeId: $prizeId")
                // 添加助力券ID到列表
                helpDataList.add(prizeId)
            }
        }

        // 更新状态
        _boostCouponHelpData.value = helpDataList

        showToast("助力券配置 保存成功")
        processRecorder?.recordProcess("保存助力券配置数据成功: $helpDataList")
        Log.d("OrderViewModel", "Boost coupon help data saved: ${_boostCouponHelpData.value}")
    }

    /**
     * 发起助力券游戏码请求
     */
    fun getGameCode(prizeId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API发起助力券游戏码请求
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.coupon.getGameCode(prizeId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseBoostCouponGameCodeResponse(result.data)
                        if (response != null && response.code == 0) {
                            val gameCode = response.data
                            copyToClipboard("助力券链接", "prizeId=$prizeId&gameCode=$gameCode")
                            processRecorder?.recordProcess("获取助力券游戏码成功: prizeId=$prizeId&gameCode=$gameCode")
                            Log.d("OrderViewModel", "Successfully fetched game code: $gameCode")

                            // 刷新助力券列表
                            getBoostCouponList()
                        } else {
                            val errorMsg = response?.message ?: "助力券发起错误"
                            processRecorder?.recordProcess(
                                "获取助力券游戏码错误: $prizeId, $errorMsg",
                                "ERROR"
                            )
                            Log.e("OrderViewModel", "Failed to get game code: $errorMsg")
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "Error fetching game code", result.error)
                        processRecorder?.recordProcess(
                            "获取助力券游戏码异常: $prizeId, ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while fetching game code", e)
                processRecorder?.recordProcess("获取助力券游戏码操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置是否显示订单查询对话框
     */
    fun setShowOrderSearchDialog(show: Boolean) {
        _showOrderSearchDialog.value = show
    }

    /**
     * 设置是否显示卡记录订单列表对话框
     */
    fun setShowCardRecordOrderListDialog(show: Boolean) {
        _showCardRecordOrderListDialog.value = show
    }

    /**
     * 设置记录订单ID
     */
    fun setRecordOrderId(orderId: String) {
        _recordOrderId.value = orderId
    }

    /**
     * 获取卡记录订单列表
     * @param pageNum 页码，默认为"1"
     * @param isLoadMore 是否是加载更多
     */
    fun getCardRecordOrderList(pageNum: String = "1", isLoadMore: Boolean = false) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API获取卡记录订单列表
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.card.getCardRecordOrder(pageNum)
                }

                when (result) {
                    is RequestResult.Success -> {
                        // 解析响应数据
                        val response =
                            ResponseParserUtils.parseCardTransInfoListByPageResponse(result.data)
                        if (response != null && response.code == 0) {
                            // 获取订单列表
                            val orderList = mutableListOf<String>()
                            val orders = response.data?.list ?: emptyList()
                            val orderIdSet = mutableSetOf<String>() // 用于去重

                            orders.forEach { order ->
                                val orderId = order.orderId
                                if (orderId.isNotEmpty() && !orderIdSet.contains(orderId)) {
                                    orderIdSet.add(orderId)
                                    val merchantName = order.merchantName
                                    val txnName = order.txnName
                                    val transTime = order.transTime
                                    val orderInfo =
                                        "$orderId,$merchantName,$txnName,$transTime".replace(
                                            "    ",
                                            ""
                                        )
                                    orderList.add(orderInfo)
                                }
                            }

                            withContext(Dispatchers.Main) {
                                // 更新分页信息
                                _cardRecordOrderListHasNextPage.value =
                                    response.data?.hasNextPage ?: false
                                _cardRecordOrderListCurrentPage.value = pageNum

                                // 如果是加载更多，则追加到现有列表
                                if (isLoadMore) {
                                    val currentList = _cardRecordOrderList.value.toMutableList()
                                    // 过滤掉已有的订单ID
                                    val existingOrderIds =
                                        currentList.map { it.split(",")[0] }.toSet()
                                    val newOrders =
                                        orderList.filter { !existingOrderIds.contains(it.split(",")[0]) }
                                    currentList.addAll(newOrders)
                                    _cardRecordOrderList.value = currentList
                                } else {
                                    _cardRecordOrderList.value = orderList
                                }

                                if (orderList.isNotEmpty() || isLoadMore) {
                                    setShowCardRecordOrderListDialog(true)
                                    processRecorder?.recordProcess("卡记录订单列表: 共${orderList.size}条, 页码: $pageNum, 是否有下一页: ${if (response.data?.hasNextPage == true) "是" else "否"}")
                                    Log.d(
                                        "OrderViewModel",
                                        "Successfully fetched card record order list: ${orderList.size}, page: $pageNum, hasNextPage: ${response.data?.hasNextPage}"
                                    )
                                } else {
                                    processRecorder?.recordProcess("卡记录订单列表: 订单记录为空")
                                    showToast("订单记录为空")
                                }
                            }
                        } else {
                            val errorMsg = response?.message ?: "获取卡记录订单列表错误"
                            Log.e(
                                "OrderViewModel",
                                "Failed to get card record order list: $errorMsg"
                            )
                            processRecorder?.recordProcess(
                                "获取卡记录订单列表错误: $errorMsg",
                                "ERROR"
                            )
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e(
                            "OrderViewModel",
                            "Error fetching card record order list",
                            result.error
                        )
                        processRecorder?.recordProcess(
                            "获取卡记录订单列表异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while fetching card record order list", e)
                processRecorder?.recordProcess("获取卡记录订单列表操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 获取可申请发票的订单列表
     * @param page 页码
     * @param size 每页大小
     */
    fun getInvoiceCanApplyOrderList(page: Int = 0, size: Int = 10) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.order.invoiceCanApplyOrderList(page, size)
                }

                withContext(Dispatchers.Main) {
                    when (result) {
                        is RequestResult.Success -> {
                            val response =
                                ResponseParserUtils.parseInvoiceCanApplyOrderListResponse(result.data)
                            if (response != null && response.code == 0) {
                                _invoiceCanApplyOrderList.value += (response.data?.orders
                                    ?: emptyList())
                                _invoiceCanApplyOrderPage.value = response.data?.page ?: 0
                                _invoiceCanApplyOrderPageCount.value = response.data?.pagecount ?: 0

                                // 显示发票可申请订单列表对话框
                                if (_invoiceCanApplyOrderList.value.isNotEmpty()) {
                                    _showInvoiceCanApplyOrderListDialog.value = true
                                } else {
                                    showToast("可申请发票的订单为空")
                                }

                                processRecorder?.recordProcess("可申请发票订单列表: 共${_invoiceCanApplyOrderList.value.size}条")
                                Log.d(
                                    "OrderViewModel",
                                    "获取可申请发票订单列表成功: ${_invoiceCanApplyOrderList.value.size} 条记录"
                                )
                            } else {
                                Log.e(
                                    "OrderViewModel",
                                    "获取可申请发票订单列表错误: ${response?.message}"
                                )
                                processRecorder?.recordProcess(
                                    "获取可申请发票订单列表错误: ${response?.message}",
                                    "ERROR"
                                )
                                showToast(response?.message ?: "获取可申请发票订单列表错误")
                            }
                        }

                        is RequestResult.Error -> {
                            Log.e("OrderViewModel", "获取可申请发票订单列表异常", result.error)
                            processRecorder?.recordProcess(
                                "获取可申请发票订单列表异常: ${result.error.message}",
                                "ERROR"
                            )
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "获取可申请发票订单列表异常", e)
                processRecorder?.recordProcess(
                    "获取可申请发票订单列表操作异常: ${e.message}",
                    "ERROR"
                )
                withContext(Dispatchers.Main) {
                    showToast("{e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置是否显示发票可申请订单列表对话框
     */
    fun setShowInvoiceCanApplyOrderListDialog(show: Boolean) {
        _showInvoiceCanApplyOrderListDialog.value = show
        if (!show) {
            _invoiceCanApplyOrderList.value = emptyList()
        }
    }

    /**
     * 获取订单详情
     * @param orderId 订单ID
     */
    fun getOrderDetail(orderId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API获取订单详情
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.order.getOrderDetail(orderId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        // 解析响应数据
                        val response = ResponseParserUtils.parseOrderDetailResponse(result.data)
                        if (response != null && response.code == 0) {
                            // 获取订单详情
                            val orderDetail = response.data
                            if (orderDetail != null) {
                                withContext(Dispatchers.Main) {
                                    // 将订单详情转换为JSONObject
                                    _orderDetailData.value = orderDetail
                                    processRecorder?.recordProcess("获取订单详情成功: ${orderDetail.baseinfo?.id}")
                                    Log.d(
                                        "OrderViewModel",
                                        "Successfully fetched order detail: ${orderDetail.baseinfo?.id}"
                                    )
                                }
                            } else {
                                showToast("订单详情为空")
                            }
                        } else {
                            val errorMsg = response?.message ?: "订单详情获取错误"
                            Log.e("OrderViewModel", "Failed to get order detail: $errorMsg")
                            processRecorder?.recordProcess(
                                "获取订单详情错误: $orderId, $errorMsg",
                                "ERROR"
                            )
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "Error fetching order detail", result.error)
                        processRecorder?.recordProcess(
                            "获取订单详情异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while fetching order detail", e)
                processRecorder?.recordProcess("获取订单详情操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置订单详情数据
     * @param orderDetail 订单详情数据，为null时表示清除
     */
    fun setOrderDetailData(orderDetail: OrderDetailData?) {
        _orderDetailData.value = orderDetail
    }

    /**
     * 设置是否显示送达照片对话框
     */
    fun setShowDeliveryPhotosDialog(show: Boolean) {
        _showDeliveryPhotosDialog.value = show
    }

    /**
     * 设置送达照片列表
     */
    fun setDeliveryPhotos(photos: List<String>) {
        _deliveryPhotos.value = photos
        if (photos.isNotEmpty()) {
            setShowDeliveryPhotosDialog(true)
        }
    }

    /**
     * 订单预支付
     * @param orderId 订单ID
     * @param payType 支付类型
     */
    fun orderPrePay(orderId: String, payType: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API进行订单预支付
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.order.orderPrePay(orderId, payType)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response = ResponseParserUtils.parseOrderPrepayResponse(result.data)
                        if (response != null && response.code == 0) {
                            when (payType) {
                                "pay.alipay.app" -> {
                                    // 支付宝支付
                                    val payInfo = response.data?.payInfo
                                    if (payInfo != null) {
                                        val url = withContext(Dispatchers.IO) {
                                            AliPayConvertUtils.convertAlipayToH5(payInfo)
                                        }
                                        //复制到剪贴板
                                        copyToClipboard("支付宝H5链接", url)
                                        processRecorder?.recordProcess("支付宝H5链接,$orderId,$url")
                                    } else {
                                        showToast("支付链接为空")
                                        processRecorder?.recordProcess("支付宝H5链接,$orderId,支付链接为空")
                                    }
                                }

                                "pay.dcep.app.pay.and.sign" -> {
                                    // 数字人名币支付
                                    val payInfo = response.data?.payInfo
                                    if (payInfo != null) {
                                        // 打开数字人名币支付
                                        val uri = payInfo.toUri()
                                        val intent = Intent(Intent.ACTION_VIEW, uri)
                                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                        startActivity(appContext, intent, null)
                                        processRecorder?.recordProcess("数字人名币支付链接,$orderId,$payInfo")
                                    } else {
                                        showToast("支付链接为空")
                                        processRecorder?.recordProcess("数字人名币支付链接,$orderId,支付链接为空")
                                    }
                                }
                            }
                        } else {
                            val errorMsg = response?.message ?: "订单预支付错误"
                            Log.e("OrderViewModel", "Failed to pre-pay order: $errorMsg")
                            processRecorder?.recordProcess(
                                "订单预支付错误: $orderId, $errorMsg",
                                "ERROR"
                            )
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "Error pre-paying order", result.error)
                        processRecorder?.recordProcess(
                            "订单预支付异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while pre-paying order", e)
                processRecorder?.recordProcess("订单预支付操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 取消订单
     * @param orderId 订单ID
     */
    fun cancelOrder(orderId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API取消订单 {"code":0,"message":"","now":1749718315524}
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.order.orderCancel(orderId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        //{"code":0,"message":"","now":1749718315524}
                        val response = ResponseParserUtils.parseOrderCancelResponse(result.data)
                        if (response != null && response.code == 0) {
                            showToast("取消订单成功")
                            processRecorder?.recordProcess("取消订单成功: $orderId")
                            //刷新订单列表
                            getOrderList()
                        } else {
                            showToast(response?.message ?: "取消订单错误")
                            processRecorder?.recordProcess(
                                "取消订单错误: $orderId, ${response?.message}",
                                "ERROR"
                            )
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "Error cancelling order", result.error)
                        processRecorder?.recordProcess(
                            "取消订单异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while cancelling order", e)
                processRecorder?.recordProcess("取消订单操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 申请退款
     * @param orderId 订单ID
     */
    fun refundOrder(orderId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API申请退款 {"code":0,"message":"","data":{"id":"1749719516504804","otherAftersaleIds":[]},"now":1749719516826}
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.order.orderAfterSales(orderId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response = ResponseParserUtils.parseOrderAfterSalesResponse(result.data)
                        if (response != null && response.code == 0) {
                            showToast("申请退款成功")
                            processRecorder?.recordProcess("申请退款成功: $orderId")
                            //刷新订单列表
                            getOrderList()
                        } else {
                            showToast(response?.message ?: "申请退款错误")
                            processRecorder?.recordProcess(
                                "申请退款错误: $orderId, ${response?.message}",
                                "ERROR"
                            )
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "Error requesting refund", result.error)
                        processRecorder?.recordProcess(
                            "申请退款异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while requesting refund", e)
                processRecorder?.recordProcess("申请退款操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 删除订单
     * @param orderId 订单ID
     */
    fun deleteOrder(orderId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API删除订单 {"code":0,"message":"订单删除成功","data":"","now":1749720115740}
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.order.orderDelete(orderId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        //{"code":0,"message":"订单删除成功","data":"","now":1749720115740}
                        val response = ResponseParserUtils.parseOrderDeleteResponse(result.data)
                        if (response != null && response.code == 0) {
                            // 保存删除记录到数据库
                            saveDeletedOrderRecord(orderId)

                            showToast("删除订单成功")
                            processRecorder?.recordProcess("删除订单成功: $orderId")

                            // 执行备份
                            try {
                                processRecorder?.recordProcess("单个订单删除完成，开始备份已删除订单数据")
                                val backupPath = DeletedOrderUtils.backupDeletedOrdersToFile(appContext)
                                if (backupPath != null) {
                                    processRecorder?.recordProcess("已删除订单数据备份完成: $backupPath")
                                    Log.d("OrderViewModel", "已删除订单数据备份完成: $backupPath")
                                } else {
                                    processRecorder?.recordProcess("没有需要备份的已删除订单数据")
                                    Log.d("OrderViewModel", "没有需要备份的已删除订单数据")
                                }
                            } catch (e: Exception) {
                                processRecorder?.recordProcess("备份已删除订单数据时发生错误: ${e.message}", "ERROR")
                                Log.e("OrderViewModel", "备份已删除订单数据时发生错误", e)
                            }

                            //刷新订单列表
                            getOrderList()
                        } else {
                            showToast(response?.message ?: "删除订单错误")
                            processRecorder?.recordProcess(
                                "删除订单错误: $orderId, ${response?.message}",
                                "ERROR"
                            )
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "Error deleting order", result.error)
                        processRecorder?.recordProcess(
                            "删除订单异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while deleting order", e)
                processRecorder?.recordProcess("删除订单操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 保存删除订单记录到数据库
     * @param orderId 订单ID
     */
    private suspend fun saveDeletedOrderRecord(orderId: String) {
        try {
            if (!::deletedOrderDao.isInitialized) {
                Log.w(
                    "OrderViewModel",
                    "DeletedOrderDao not initialized, skip saving deleted order record"
                )
                return
            }

            val currentAccount = _currentSelectedAccount.value
            if (currentAccount == null) {
                Log.w("OrderViewModel", "Current account is null, skip saving deleted order record")
                return
            }

            // 查找当前订单信息
            val currentOrder = _orderList.value.find { it.id == orderId }

            // 创建删除记录
            val currentTime = System.currentTimeMillis()
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val deleteDate = dateFormat.format(currentTime)

            val deletedOrderRecord = DeletedOrderEntity(
                orderId = orderId,
                uid = currentAccount.uid,
                phoneNumber = currentAccount.phoneNumber,
                orderTitle = currentOrder?.title ?: "",
                orderStatus = currentOrder?.status ?: 0,
                deleteTimestamp = currentTime,
                deleteDate = deleteDate,
                extraNote = ""
            )

            // 保存到数据库
            withContext(Dispatchers.IO) {
                deletedOrderDao.insertDeletedOrder(deletedOrderRecord)
            }

            processRecorder?.recordProcess("删除订单记录已保存: $orderId")
            Log.d("OrderViewModel", "Deleted order record saved: $orderId")

        } catch (e: Exception) {
            Log.e("OrderViewModel", "Failed to save deleted order record: ${e.message}", e)
            processRecorder?.recordProcess("保存删除订单记录错误: ${e.message}", "ERROR")
        }
    }

    /**
     * 清空订单
     */
    fun clearOrder() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 筛选需要清空的订单 6 已取消 5 已完成 16 已关闭 只清空已加载的订单
                val needClearOrderList =
                    _orderList.value.filter { it.status == 6 || it.status == 5 || it.status == 16 }
                // 删除需要清空的订单
                var clearOrderErrorCount = 0
                var clearOrderSuccessCount = 0
                for (order in needClearOrderList) {
                    val result = withContext(Dispatchers.IO) {
                        currentRequestService.order.orderDelete(order.id)
                    }
                    when (result) {
                        is RequestResult.Success -> {
                            val response = ResponseParserUtils.parseOrderDeleteResponse(result.data)
                            if (response != null && response.code == 0) {
                                // 保存删除记录到数据库
                                saveDeletedOrderRecord(order.id)

                                processRecorder?.recordProcess("删除订单成功: ${order.id}")
                                clearOrderSuccessCount++
                            } else {
                                processRecorder?.recordProcess(
                                    "删除订单错误: ${order.id}, ${response?.message}",
                                    "ERROR"
                                )
                                clearOrderErrorCount++
                            }
                        }

                        is RequestResult.Error -> {
                            Log.e("OrderViewModel", "Error deleting order", result.error)
                            processRecorder?.recordProcess(
                                "删除订单异常: ${result.error.message}",
                                "ERROR"
                            )
                            showToast("${result.error.message}")
                        }
                    }
                }

                val showToastMsg = StringBuilder()
                if (clearOrderSuccessCount > 0) {
                    showToastMsg.append("成功删除${clearOrderSuccessCount}个订单")
                }
                if (clearOrderErrorCount > 0) {
                    showToastMsg.append("，失败${clearOrderErrorCount}个")
                }
                processRecorder?.recordProcess("清空订单结果: $showToastMsg")
                showToast(showToastMsg.toString())

                // 如果有成功删除的订单，执行备份
                if (clearOrderSuccessCount > 0) {
                    try {
                        processRecorder?.recordProcess("清空订单操作完成，开始备份已删除订单数据")
                        val backupPath = DeletedOrderUtils.backupDeletedOrdersToFile(appContext)
                        if (backupPath != null) {
                            processRecorder?.recordProcess("已删除订单数据备份完成: $backupPath")
                            Log.d("OrderViewModel", "已删除订单数据备份完成: $backupPath")
                        } else {
                            processRecorder?.recordProcess("没有需要备份的已删除订单数据")
                            Log.d("OrderViewModel", "没有需要备份的已删除订单数据")
                        }
                    } catch (e: Exception) {
                        processRecorder?.recordProcess("备份已删除订单数据时发生错误: ${e.message}", "ERROR")
                        Log.e("OrderViewModel", "备份已删除订单数据时发生错误", e)
                    }
                }

                // 刷新订单列表
                getOrderList()
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while clearing order", e)
                processRecorder?.recordProcess("清空订单操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 检查是否可以修改订单配送信息
     * @param orderId 订单ID
     */
    fun checkCanUpdateDeliveryInfo(orderId: String, shopId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API检查是否可以修改订单配送信息
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.order.canUpdateDeliveryInfo(orderId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseUpdateDeliveryInfoResponse(result.data)
                        if (response != null && response.code == 0) {
                            if (response.data?.isCanUpdate == true) {
                                getOrderDeliveryInfo(orderId, shopId)
                            } else {
                                showToast("订单不能修改配送信息")
                                processRecorder?.recordProcess("检查是否可以修改订单配送信息成功: $orderId, 订单不能修改配送信息")
                            }
                        } else {
                            val errorMsg = response?.message ?: "检查是否可以修改订单配送信息错误"
                            Log.e(
                                "OrderViewModel",
                                "Failed to check if order can be updated: $errorMsg"
                            )
                            processRecorder?.recordProcess(
                                "检查是否可以修改订单配送信息错误: $orderId, $errorMsg",
                                "ERROR"
                            )
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e(
                            "OrderViewModel",
                            "Error checking if order can be updated",
                            result.error
                        )
                        processRecorder?.recordProcess(
                            "检查是否可以修改订单配送信息异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }

            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while checking if order can be updated", e)
                withContext(Dispatchers.Main) {
                    processRecorder?.recordProcess(
                        "检查是否可以修改订单配送信息操作异常: ${e.message}",
                        "ERROR"
                    )
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置是否显示订单配送信息对话框
     */
    fun setShowOrderDeliveryInfoDialog(show: Boolean) {
        _showOrderDeliveryInfoDialog.value = show
        if (!show) {
            _availableAddress.value = emptyList()
            _orderDeliveryInfo.value = null
        }
    }

    /**
     * 获取订单配送信息
     * @param orderId 订单ID
     */
    private fun getOrderDeliveryInfo(orderId: String, shopId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {

                // 获取可用地址
                val availableAddressResult = withContext(Dispatchers.IO) {
                    currentRequestService.address.getAllAddress(shopId)
                }

                when (availableAddressResult) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseAddressResponse(availableAddressResult.data)
                        if (response != null && response.code == 0) {
                            val availableAddress = response.data
                            if (availableAddress != null) {
                                processRecorder?.recordProcess("修改订单配送信息获取可用地址成功: $orderId, 共${availableAddress.list.size}条")
                                _availableAddress.value = availableAddress.list
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e(
                            "OrderViewModel",
                            "Error getting available address",
                            availableAddressResult.error
                        )
                        processRecorder?.recordProcess(
                            "修改订单配送信息获取可用地址异常: $orderId, ${availableAddressResult.error.message}",
                            "ERROR"
                        )
                        showToast("${availableAddressResult.error.message}")
                    }
                }

                // 调用API获取订单配送信息
                val deliveryInfoResult = withContext(Dispatchers.IO) {
                    currentRequestService.order.getOrderDeliveryInfo(orderId)
                }

                when (deliveryInfoResult) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseOrderDeliveryInfoResponse(deliveryInfoResult.data)
                        if (response != null && response.code == 0) {
                            val orderDeliveryInfo = response.data
                            if (orderDeliveryInfo != null) {
                                _orderDeliveryInfo.value = orderDeliveryInfo
                                _showOrderDeliveryInfoDialog.value = true
                                processRecorder?.recordProcess("修改订单配送信息获取订单配送信息成功: $orderId")
                            } else {
                                showToast("订单配送信息为空")
                                processRecorder?.recordProcess("修改订单配送信息获取订单配送信息成功: $orderId, 订单配送信息为空")
                            }
                        } else {
                            val errorMsg = response?.message ?: "获取订单配送信息错误"
                            Log.e("OrderViewModel", "Failed to get order delivery info: $errorMsg")
                            processRecorder?.recordProcess(
                                "修改订单配送信息获取订单配送信息错误: $orderId, $errorMsg",
                                "ERROR"
                            )
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e(
                            "OrderViewModel",
                            "Error getting order delivery info",
                            deliveryInfoResult.error
                        )
                        processRecorder?.recordProcess(
                            "获取订单配送信息异常: ${deliveryInfoResult.error.message}",
                            "ERROR"
                        )
                        showToast("${deliveryInfoResult.error.message}")
                    }
                }

            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while getting order delivery info", e)
                processRecorder?.recordProcess("获取订单配送信息操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 更新订单配送信息
     * @param modifyReturnParamJSONObject 修改的信息
     */
    fun updateDeliveryInfo(
        modifyReturnParamJSONObject: JSONObject
    ) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            try {
                // 调用API更新配送信息
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.order.updateDeliveryInfo(modifyReturnParamJSONObject)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseUpdateDeliveryInfoResponse(result.data)
                        if (response != null && response.code == 0) {
                            val msg = response.data?.msg
                            if (msg != null) {
                                showToast(msg)
                                //刷新订单详情
                                processRecorder?.recordProcess("更新配送信息成功: $msg")
                                val orderId = modifyReturnParamJSONObject.optString("orderId")
                                if (orderId.isNotEmpty()) {
                                    getOrderDetail(orderId)
                                }
                            } else {
                                showToast("未知修改结果")
                                processRecorder?.recordProcess("更新配送信息成功: 未知修改结果")
                            }

                        } else {
                            val errorMsg = response?.message ?: "更新配送信息错误"
                            Log.e("OrderViewModel", "Failed to update delivery info: $errorMsg")
                            processRecorder?.recordProcess("更新配送信息错误: $errorMsg", "ERROR")
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "updateDeliveryInfo error", result.error)
                        processRecorder?.recordProcess(
                            "更新配送信息异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "updateDeliveryInfo error", e)
                processRecorder?.recordProcess("更新配送信息操作异常: ${e.message}", "ERROR")
                showToast("${e.message}")
            }
        }
    }

    /**
     * 瓜分红包
     * @param orderId 订单ID
     */
    fun orderRedEnvelope(orderId: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                // 调用API瓜分红包
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.order.orderRedEnvelope(orderId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        // 解析响应数据
                        val response =
                            ResponseParserUtils.parseOrderRedEnvelopeCheckResponse(result.data)
                        if (response != null && response.code == 0) {
                            if (response.data == null) {
                                showToast("瓜分红包参数为空")
                                processRecorder?.recordProcess("瓜分红包参数为空: $orderId")
                                return@launch
                            }
                            val eventIdStr = "eventId=" + response.data.bunchid
                            // 复制到剪贴板
                            copyToClipboard("瓜分红包参数", eventIdStr)
                            processRecorder?.recordProcess("瓜分红包参数: $orderId, $eventIdStr")
                        } else {
                            val errorMsg = response?.message ?: "瓜分红包错误"
                            Log.e("OrderViewModel", "Failed to get red envelope: $errorMsg")
                            processRecorder?.recordProcess(
                                "瓜分红包错误: $orderId, $errorMsg",
                                "ERROR"
                            )
                            showToast(errorMsg)
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "Error getting red envelope", result.error)
                        processRecorder?.recordProcess(
                            "瓜分红包异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while getting red envelope", e)
                processRecorder?.recordProcess("瓜分红包操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 保存商品配置
     * @param productConfig 商品配置字符串
     */
    fun saveOrderProductConfig(productConfig: String) {
        // 保存商品配置到本地
        viewModelScope.launch {
            try {
                _productConfigStr.value = productConfig
                showToast("商品配置保存成功")
                processRecorder?.recordProcess("商品配置保存成功: $productConfig")
                Log.d("OrderViewModel", "Successfully saved product config: $productConfig")
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Exception while saving product config", e)
                processRecorder?.recordProcess("保存商品配置错误: ${e.message}", "ERROR")
                showToast("保存商品配置失败: ${e.message}")
            }
        }
    }

    /**
     * 显示取消赠送永辉卡对话框
     * @param card 要取消赠送的卡
     */
    fun showCancelSendCardDialog(card: CardItem) {
        _selectedCardToCancel.value = card
        _showCancelSendCardDialog.value = true
    }

    /**
     * 设置是否显示取消赠送永辉卡对话框
     */
    fun setShowCancelSendCardDialog(show: Boolean) {
        _showCancelSendCardDialog.value = show
        if (!show) {
            _selectedCardToCancel.value = null
        }
    }

    /**
     * 显示赠送永辉卡确认对话框
     * @param card 要赠送的卡
     */
    fun showSendCardDialog(card: CardItem) {
        _selectedCardToSend.value = card
        _showSendCardDialog.value = true
    }

    /**
     * 设置是否显示赠送永辉卡确认对话框
     */
    fun setShowSendCardDialog(show: Boolean) {
        _showSendCardDialog.value = show
        if (!show) {
            _selectedCardToSend.value = null
        }
    }

    /**
     * 显示优惠券详情对话框
     * @param coupon 优惠券
     */
    fun showCouponDetailDialog(coupon: CouponListCoupon) {
        _selectedCoupon.value = coupon
        _showCouponDetailDialog.value = true
    }

    /**
     * 设置是否显示优惠券详情对话框
     */
    fun setShowCouponDetailDialog(show: Boolean) {
        _showCouponDetailDialog.value = show
        if (!show) {
            _selectedCoupon.value = null
        }
    }

    /**
     * 显示助力券详情对话框
     * @param boostCoupon 助力券记录
     */
    fun showBoostCouponDetailDialog(boostCoupon: BoostCouponRecord) {
        _selectedBoostCoupon.value = boostCoupon
        _showBoostCouponDetailDialog.value = true
    }

    /**
     * 设置是否显示助力券详情对话框
     * @param show 是否显示
     */
    fun setShowBoostCouponDetailDialog(show: Boolean) {
        _showBoostCouponDetailDialog.value = show
        if (!show) {
            _selectedBoostCoupon.value = null
        }
    }

    /**
     * 获取售后订单列表
     * @param page 页码
     * @param size 每页数量
     */
    fun getAfterSalesList(page: Int = 0, size: Int = 10) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.order.afterSalesList(page, size)
                }

                withContext(Dispatchers.Main) {
                    when (result) {
                        is RequestResult.Success -> {
                            val response =
                                ResponseParserUtils.parseAfterSalesListResponse(result.data)
                            if (response != null && response.code == 0) {
                                if (page == 0) {
                                    // 如果是第一页，则替换现有数据
                                    _afterSalesOrderList.value = response.data?.list ?: emptyList()
                                } else {
                                    // 如果是加载更多，则追加数据
                                    _afterSalesOrderList.value += response.data?.list ?: emptyList()
                                }

                                _afterSalesOrderPage.value = response.data?.page ?: 0
                                _afterSalesOrderPageCount.value = response.data?.pagecount ?: 0

                                // 显示售后订单列表对话框
                                if (_afterSalesOrderList.value.isNotEmpty()) {
                                    _showAfterSalesListDialog.value = true
                                } else {
                                    showToast("售后记录为空")
                                }

                                processRecorder?.recordProcess("售后订单列表: 共${_afterSalesOrderList.value.size}条")
                                Log.d(
                                    "OrderViewModel",
                                    "获取售后订单列表成功: ${_afterSalesOrderList.value.size} 条记录"
                                )
                            } else {
                                processRecorder?.recordProcess(
                                    "获取售后订单列表错误: ${response?.message}",
                                    "ERROR"
                                )
                                Log.e(
                                    "OrderViewModel",
                                    "获取售后订单列表错误: ${response?.message}"
                                )
                                showToast(response?.message ?: "获取售后订单列表错误")
                            }
                        }

                        is RequestResult.Error -> {
                            Log.e("OrderViewModel", "获取售后订单列表错误", result.error)
                            processRecorder?.recordProcess(
                                "获取售后订单列表异常: ${result.error.message}",
                                "ERROR"
                            )
                            showToast("${result.error.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "获取售后订单列表异常", e)
                processRecorder?.recordProcess("获取售后订单列表操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置是否显示售后订单列表对话框
     */
    fun setShowAfterSalesListDialog(show: Boolean) {
        _showAfterSalesListDialog.value = show
        if (!show) {
            _afterSalesOrderList.value = emptyList()
        }
    }

    /**
     * 获取邀请活动信息
     * @param type 活动类型
     * @param onSuccess 成功回调
     */
    fun getInvitationV2ActiveInfo(
        type: String,
        onSuccess: (InvitationV2ActivityInfoData) -> Unit = {}
    ) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.invitation.invitationV2ActivityInfo()
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseInvitationV2ActiveInfoResponse(result.data)
                        if (response != null && response.code == 0) {
                            val baseInfo = response.data?.baseinfo
                            if (baseInfo != null) {
                                when (type) {
                                    "show_activity_rule" -> {
                                        val activityRule = buildInvitationActivityRule(baseInfo)
                                        _commonActivityRule.value = activityRule
                                        _showCommonActivityRuleDialog.value = true
                                        processRecorder?.recordProcess("获取邀请活动信息成功: $activityRule")
                                    }

                                    "copy_invitation_code" -> {
                                        onSuccess(response.data)
                                    }
                                }
                            } else {
                                processRecorder?.recordProcess(
                                    "获取邀请活动信息错误: ${response.message}",
                                    "ERROR"
                                )
                                showToast("获取邀请活动信息错误")
                            }
                        } else {
                            processRecorder?.recordProcess(
                                "获取邀请活动信息错误: ${response?.message}",
                                "ERROR"
                            )
                            showToast(response?.message ?: "获取邀请活动信息错误")
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "获取邀请活动信息异常", result.error)
                        processRecorder?.recordProcess(
                            "获取邀请活动信息异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast("${result.error.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "获取邀请活动信息异常", e)
                processRecorder?.recordProcess("获取邀请活动信息操作异常: ${e.message}", "ERROR")
                withContext(Dispatchers.Main) {
                    showToast("${e.message}")
                }
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 复制邀请码
     */
    fun copyInvitationCode() {
        getInvitationV2ActiveInfo(type = "copy_invitation_code") {
            val invitationCode = it.memberId + "," + it.baseinfo?.activitycode + "," + it.shopId
            copyToClipboard("邀请码", invitationCode)
            processRecorder?.recordProcess("复制邀请码成功: $invitationCode")
        }
    }

    /**
     * 设置是否显示绑定邀请码对话框
     */
    fun setShowBindInvitationCodeDialog(show: Boolean) {
        _showBindInvitationCodeDialog.value = show
    }

    /**
     * 保存邀请码
     * @param invitationCode 邀请码
     */
    fun saveInvitationCode(invitationCode: String) {
        _bindInvitationCode.value = invitationCode
    }

    /**
     * 绑定邀请码
     * @param invitationCodeStr 邀请码
     */
    fun bindInvitationCode(invitationCodeStr: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                val invitationCodeList = invitationCodeStr.split(",")
                val uid = invitationCodeList[0]
                val code = invitationCodeList[1]
                val shopId = invitationCodeList[2]

                val result = withContext(Dispatchers.IO) {
                    currentRequestService.invitation.invitationV2Bind(uid, code, shopId)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseInvitationV2BindResponse(result.data)
                        if (response != null && response.code == 0) {
                            val data = response.data
                            val resultCode = data?.resultCode
                            val showMsg = data?.showMsg
                            showMsg?.let { showToast(it) }
                            processRecorder?.recordProcess("绑定邀请码成功: $invitationCodeStr, $showMsg")
                        } else {
                            showToast(response?.message ?: "绑定邀请码错误")
                            processRecorder?.recordProcess(
                                "绑定邀请码错误: $invitationCodeStr, ${response?.message}",
                                "ERROR"
                            )
                        }
                    }

                    is RequestResult.Error -> {
                        showToast("绑定邀请码异常")
                        processRecorder?.recordProcess(
                            "绑定邀请码异常: ${result.error.message}",
                            "ERROR"
                        )
                    }
                }
            } catch (e: Exception) {
                showToast("绑定邀请码异常")
                processRecorder?.recordProcess("绑定邀请码操作异常: ${e.message}", "ERROR")
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    private fun buildInvitationActivityRule(info: InvitationV2ActivityInfoBaseInfo): String {
        // 获取操作类型名称，如"下单"
        val validOperationTypeName = info.validOperationTypeName
        // 获取订单支付限制金额
        val orderPayLimit = info.orderPayLimit
        // 获取有效期（小时）
        val validperiod = info.validperiod
        // 获取活动说明，并去除空格
        val description = info.description.replace(" ", "")

        // 构建奖励规则字符串
        var rewardruleStr = "1、邀请奖励: "
        info.rewardrule.forEach { item ->
            // 判断被邀请人类型
            val inviteetypeStr = if (item.inviteetype == "new") "新用户" else "老用户"
            // 奖励门槛描述
            val inviterrewardlimit = item.inviterrewardlimit
            // 奖励内容
            val inviterrewardcontent = item.inviterrewardcontent
            // 拼接奖励规则
            rewardruleStr += "邀请${inviteetypeStr}奖励$inviterrewardlimit￥$inviterrewardcontent; "
        }

        // 拼接完整活动规则字符串
        return "$rewardruleStr\n2、成功条件: 实付金额满${orderPayLimit}元，在${validperiod}小时内$validOperationTypeName; \n3、活动说明: \n$description"
    }

    /**
     * 获取签到奖励规则
     */
    fun getSignRewardRule() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.credit.signRewardDetail()
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseSignRewardDetailResponse(result.data)
                        if (response != null && response.code == 0) {
                            if (response.data == null) {
                                showToast("签到奖励规则参数为空")
                                processRecorder?.recordProcess("签到奖励规则参数为空")
                                return@launch
                            }
                            val dividepointvalue = response.data.dividepointvalue
                            val rule = "组队瓜分 $dividepointvalue 积分"
                            if (rule.isNotEmpty()) {
                                processRecorder?.recordProcess("签到奖励规则: $rule")
                                _commonActivityRule.value = rule
                                _showCommonActivityRuleDialog.value = true
                            } else {
                                showToast("未获取到签到奖励规则")
                                processRecorder?.recordProcess("获取签到奖励规则成功: 未获取到签到奖励规则")
                            }
                        } else {
                            showToast(response?.message ?: "获取签到奖励规则错误")
                            processRecorder?.recordProcess(
                                "获取签到奖励规则错误: ${response?.message}",
                                "ERROR"
                            )
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "获取签到奖励规则异常", result.error)
                        processRecorder?.recordProcess(
                            "获取签到奖励规则异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast(result.error.message ?: "获取签到奖励规则错误")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "获取签到奖励规则异常", e)
                processRecorder?.recordProcess("获取签到奖励规则操作异常: ${e.message}", "ERROR")
                showToast(e.message ?: "获取签到奖励规则异常")
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 获取组队瓜分信息
     */
    fun getPointTeam() {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)
            try {
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.credit.pointTeam()
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseDividePointTeamResponse(result.data)
                        if (response != null && response.code == 0) {
                            val teamCode = response.data?.teamCode ?: ""
                            if (teamCode.isNotEmpty()) {
                                val teamCodeStr = "teamCode=$teamCode"
                                copyToClipboard("组队瓜分", teamCodeStr)
                                processRecorder?.recordProcess("组队瓜分信息: $teamCodeStr")
                            } else {
                                showToast("未获取到组队瓜分信息")
                                processRecorder?.recordProcess("获取组队瓜分信息成功: 未获取到组队瓜分信息")
                            }
                        } else {
                            showToast(response?.message ?: "获取组队瓜分信息错误")
                            processRecorder?.recordProcess(
                                "获取组队瓜分信息错误: ${response?.message}",
                                "ERROR"
                            )
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "获取组队瓜分信息异常", result.error)
                        processRecorder?.recordProcess(
                            "获取组队瓜分信息异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast(result.error.message ?: "获取组队瓜分信息异常")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "获取组队瓜分信息异常", e)
                processRecorder?.recordProcess("获取组队瓜分信息操作异常: ${e.message}", "ERROR")
                showToast(e.message ?: "获取组队瓜分信息异常")
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置是否显示加入组队瓜分对话框
     */
    fun setShowJoinPointTeamDialog(show: Boolean) {
        _showJoinPointTeamDialog.value = show
    }

    /**
     * 设置组队瓜分团队代码
     */
    fun setPointTeamCode(teamCode: String) {
        _pointTeamCode.value = teamCode
    }

    /**
     * 加入组队瓜分
     * @param teamCode 团队代码
     */
    fun joinPointTeam(teamCode: String) {
        viewModelScope.launch {
            val currentRequestService = requestService
            if (currentRequestService == null) {
                showToast("服务未初始化")
                return@launch
            }

            setLoadingTokenInfo(true)

            try {
                val result = withContext(Dispatchers.IO) {
                    currentRequestService.credit.joinPointTeam(teamCode)
                }

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseDividePointJoinTeamResponse(result.data)
                        if (response != null && response.code == 0) {
                            val data = response.data
                            val isSuccess = data?.isSuccess
                            val description = data?.description ?: "加入组队瓜分错误"
                            if (isSuccess == true) {
                                showToast("加入组队瓜分成功")
                                processRecorder?.recordProcess("加入组队瓜分成功: $teamCode")
                                _showJoinPointTeamDialog.value = false
                            } else {
                                showToast(description)
                                processRecorder?.recordProcess(
                                    "加入组队瓜分错误: $teamCode, $description",
                                    "ERROR"
                                )
                            }
                        } else {
                            showToast(response?.message ?: "加入组队瓜分错误")
                            processRecorder?.recordProcess(
                                "加入组队瓜分错误: $teamCode, ${response?.message}",
                                "ERROR"
                            )
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e("OrderViewModel", "加入组队瓜分异常", result.error)
                        processRecorder?.recordProcess(
                            "加入组队瓜分异常: ${result.error.message}",
                            "ERROR"
                        )
                        showToast(result.error.message ?: "加入组队瓜分异常")
                    }
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "加入组队瓜分异常", e)
                processRecorder?.recordProcess("加入组队瓜分操作异常: ${e.message}", "ERROR")
                showToast(e.message ?: "加入组队瓜分异常")
            } finally {
                setLoadingTokenInfo(false)
            }
        }
    }

    /**
     * 设置日志仓库
     * @param repository 日志仓库实现
     */
    fun setLogRepository(repository: LogRepository) {
        logRepository = repository
    }

    /**
     * 设置删除订单DAO
     * @param dao 删除订单DAO实现
     */
    fun setDeletedOrderDao(dao: DeletedOrderDao) {
        deletedOrderDao = dao
    }

    /**
     * 获取所有删除订单记录
     */
    fun getAllDeletedOrders(): Flow<List<DeletedOrderEntity>>? {
        return if (::deletedOrderDao.isInitialized) {
            deletedOrderDao.getAllDeletedOrders()
        } else {
            Log.e("OrderViewModel", "DeletedOrderDao not initialized")
            null
        }
    }

    /**
     * 根据账号UID获取删除订单记录
     */
    fun getDeletedOrdersByUid(uid: String): Flow<List<DeletedOrderEntity>>? {
        return if (::deletedOrderDao.isInitialized) {
            deletedOrderDao.getDeletedOrdersByUid(uid)
        } else {
            Log.e("OrderViewModel", "DeletedOrderDao not initialized")
            null
        }
    }

    /**
     * 根据手机号获取删除订单记录
     */
    fun getDeletedOrdersByPhoneNumber(phoneNumber: String): Flow<List<DeletedOrderEntity>>? {
        return if (::deletedOrderDao.isInitialized) {
            deletedOrderDao.getDeletedOrdersByPhoneNumber(phoneNumber)
        } else {
            Log.e("OrderViewModel", "DeletedOrderDao not initialized")
            null
        }
    }

    /**
     * 检查订单是否已被删除
     */
    suspend fun isOrderDeleted(orderId: String): Boolean {
        return if (::deletedOrderDao.isInitialized) {
            try {
                deletedOrderDao.isOrderDeleted(orderId)
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Failed to check if order is deleted: ${e.message}")
                false
            }
        } else {
            Log.e("OrderViewModel", "DeletedOrderDao not initialized")
            false
        }
    }

    /**
     * 获取删除订单记录总数
     */
    suspend fun getDeletedOrderCount(): Int {
        return if (::deletedOrderDao.isInitialized) {
            try {
                deletedOrderDao.getDeletedOrderCount()
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Failed to get deleted order count: ${e.message}")
                0
            }
        } else {
            Log.e("OrderViewModel", "DeletedOrderDao not initialized")
            0
        }
    }

    /**
     * 获取指定账号的删除订单记录总数
     */
    suspend fun getDeletedOrderCountByUid(uid: String): Int {
        return if (::deletedOrderDao.isInitialized) {
            try {
                deletedOrderDao.getDeletedOrderCountByUid(uid)
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Failed to get deleted order count by uid: ${e.message}")
                0
            }
        } else {
            Log.e("OrderViewModel", "DeletedOrderDao not initialized")
            0
        }
    }

    /**
     * 显示删除订单记录对话框
     */
    fun showDeletedOrderRecordsDialog() {
        _showDeletedOrderRecordsDialog.value = true
    }

    /**
     * 隐藏删除订单记录对话框
     */
    fun hideDeletedOrderRecordsDialog() {
        _showDeletedOrderRecordsDialog.value = false
    }

    /**
     * 加载当前账号的删除订单记录
     */
    fun loadDeletedOrderRecords() {
        val currentAccount = _currentSelectedAccount.value
        if (currentAccount == null) {
            Log.w("OrderViewModel", "Current account is null, cannot load deleted order records")
            return
        }

        viewModelScope.launch {
            try {
                if (::deletedOrderDao.isInitialized) {
                    // 使用 first() 只获取一次数据，而不是持续监听
                    val records = deletedOrderDao.getDeletedOrdersByUid(currentAccount.uid).first()
                    _deletedOrderRecords.value = records
                    if (records.isNotEmpty()) {
                        showDeletedOrderRecordsDialog()
                    } else {
                        showToast("删除订单记录为空")
                    }
                    Log.d("OrderViewModel", "deletedOrderRecords loaded: ${records.size} records")
                } else {
                    Log.e("OrderViewModel", "DeletedOrderDao not initialized")
                    _deletedOrderRecords.value = emptyList()
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Failed to load deleted order records: ${e.message}")
                _deletedOrderRecords.value = emptyList()
                showToast("加载删除记录错误: ${e.message}")
            }
        }
    }

    /**
     * 开始监听当前账号的删除订单记录（实时更新）
     */
    fun startObservingDeletedOrderRecords() {
        val currentAccount = _currentSelectedAccount.value
        if (currentAccount == null) {
            Log.w("OrderViewModel", "Current account is null, cannot observe deleted order records")
            return
        }

        viewModelScope.launch {
            try {
                if (::deletedOrderDao.isInitialized) {
                    // 持续监听数据变化
                    deletedOrderDao.getDeletedOrdersByUid(currentAccount.uid).collect { records ->
                        _deletedOrderRecords.value = records
                        Log.d(
                            "OrderViewModel",
                            "deletedOrderRecords updated: ${records.size} records"
                        )
                    }
                } else {
                    Log.e("OrderViewModel", "DeletedOrderDao not initialized")
                    _deletedOrderRecords.value = emptyList()
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Failed to observe deleted order records: ${e.message}")
                _deletedOrderRecords.value = emptyList()
            }
        }
    }

    /**
     * 清空当前账号的所有删除订单记录
     */
    fun clearAllDeletedOrderRecords() {
        val currentAccount = _currentSelectedAccount.value
        if (currentAccount == null) {
            Log.w("OrderViewModel", "Current account is null, cannot clear deleted order records")
            return
        }

        viewModelScope.launch {
            try {
                if (::deletedOrderDao.isInitialized) {
                    withContext(Dispatchers.IO) {
                        deletedOrderDao.deleteDeletedOrdersByUid(currentAccount.uid)
                    }
                    showToast("删除记录已清空")
                    processRecorder?.recordProcess("清空删除订单记录: ${currentAccount.uid}")

                    // 刷新记录列表
                    _deletedOrderRecords.value = emptyList()
                } else {
                    Log.e("OrderViewModel", "DeletedOrderDao not initialized")
                    showToast("数据库未初始化")
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Failed to clear deleted order records: ${e.message}")
                showToast("清空删除记录失败: ${e.message}")
                processRecorder?.recordProcess("清空删除订单记录失败: ${e.message}", "ERROR")
            }
        }
    }

    /**
     * 获取指定token的日志
     * @param tokenUid token的唯一标识符
     * @param logLevel 日志级别 (可选)
     * @param offset 分页起始位置
     * @param limit 分页大小
     * @return 日志列表
     */
    suspend fun getLogsByToken(
        tokenUid: String,
        logLevel: String? = null,
        offset: Int,
        limit: Int
    ): List<LogEntity> {
        return if (::logRepository.isInitialized) {
            try {
                logRepository.getLogsByTokenWithPagination(tokenUid, logLevel, offset, limit)
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Failed to get logs for token $tokenUid: ${e.message}")
                emptyList()
            }
        } else {
            Log.e("OrderViewModel", "LogRepository not initialized")
            emptyList()
        }
    }

    /**
     * 将ConfigTokenEntity保存到SharedPreferences
     */
    @SuppressLint("WorldReadableFiles")
    internal fun saveOrderTokenToPrefs(orderToken: OrderTokenEntity) {
        try {
            // 使用XPrefsWriter写入配置
            XPrefsWriter.writeConfig(
                getApplication(),
                orderToken
            )
        } catch (e: Exception) {
            Log.e("MainViewModel", "error saving to XSharedPreferences", e)
        }
    }

    /**
     * 将订单令牌移至列表最后
     * @param token 要移动的令牌
     */
    fun moveOrderTokenToLast(token: OrderTokenEntity) {
        viewModelScope.launch {
            try {
                // 保存当前令牌和它的原始排序值，用于撤回操作
                val originalToken = token.copy()
                _lastMovedToken.value = originalToken

                // 获取当前最大的排序值并加1，确保移动到最后
                val maxSortOrder = _orderTokens.value.maxByOrNull { it.sortOrder }?.sortOrder
                    ?: System.currentTimeMillis()
                val updatedToken = token.copy(sortOrder = maxSortOrder + 1)

                // 更新数据库中的令牌
                tokenRepository.updateOrderToken(updatedToken)

                // 设置滚动到的令牌
                _scrollToTokenUid.value = token.uid

                showToast("账号已移至列表最后")
                Log.d("OrderViewModel", "Token moved to last: ${token.phoneNumber}")
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Error moving token to last", e)
                showToast("移动账号错误")
            }
        }
    }

    /**
     * 撤回最后一次移动的令牌操作
     * @param token 要撤回的令牌
     */
    fun revokeOrderToken(token: OrderTokenEntity) {
        viewModelScope.launch {
            try {
                val lastMovedToken = _lastMovedToken.value
                if (lastMovedToken != null && lastMovedToken.uid == token.uid) {
                    // 恢复令牌的原始排序值
                    val originalSortOrder = lastMovedToken.sortOrder
                    val updatedToken = token.copy(sortOrder = originalSortOrder)

                    // 更新数据库中的令牌
                    tokenRepository.updateOrderToken(updatedToken)

                    // 设置滚动到的令牌
                    _scrollToTokenUid.value = token.uid

                    // 重置撤回状态
                    _lastMovedToken.value = null

                    showToast("已撤回移动操作")
                    Log.d("OrderViewModel", "Token move revoked: ${token.phoneNumber}")
                } else {
                    showToast("没有可撤回的移动操作")
                    Log.d("OrderViewModel", "No token move to revoke")
                }
            } catch (e: Exception) {
                Log.e("OrderViewModel", "Error revoking token move", e)
                showToast("撤回操作错误")
            }
        }
    }

    /**
     * 重置滚动到的令牌状态
     */
    fun resetScrollToToken() {
        _scrollToTokenUid.value = ""
    }

    /**
     * 显示添加账号对话框
     * @param clipboardContent 剪贴板内容
     */
    fun showAddAccountDialog(clipboardContent: String) {
        _addAccountContent.value = clipboardContent
        _showAddAccountDialog.value = true
    }

    /**
     * 设置是否显示添加账号对话框
     */
    fun setShowAddAccountDialog(show: Boolean) {
        _showAddAccountDialog.value = show
    }

    /**
     * 处理剪贴板文本变化
     */
    fun handleClipboardTextChange() {
        val clipboardText = clipboardManager.primaryClip?.getItemAt(0)?.text?.toString()
        Log.d("OrderViewModel", "clipboardText: $clipboardText")
        if (clipboardText != null && clipboardText != addAccountContent.value) {
            if (clipboardText.contains("----") && clipboardText.split("----").size in 5..6) {
                val uid = clipboardText.split("----")[1]
                if (uid.isNotEmpty() && uid.all { it.isDigit() }) {
                    val token = orderTokens.value.find { it.uid == uid }
                    if (token != null) {
                        return
                    }
                    showAddAccountDialog(clipboardText)
                }
            }
        }
    }

    /**
     * 复制配置到剪贴板
     * @param inputStr 配置
     * @param label 剪贴板标签
     */
    fun onCopyConfig(inputStr: String, label: String) {
        if (inputStr.isNotEmpty()) {
            val encryptedString = EncryptionUtils.encrypt(inputStr)
            ClipboardUtils.copyTextToClipboard(
                clipboardManager,
                encryptedString,
                label
            )
            showToast("已复制到剪贴板")
        } else {
            showToast("请先输入配置信息")
        }
    }

    /**
     * 处理剪贴板文本添加商品对话框
     */
    fun handleClipboardTextAddProduct() {
        val clipboardText = clipboardManager.primaryClip?.getItemAt(0)?.text?.toString()
        // 尝试解密剪贴板内容
        val decryptedText = clipboardText?.let { EncryptionUtils.decrypt(it) }
        Log.d("OrderViewModel", "decryptedText: $decryptedText")
        decryptedText?.let {
            if (it.isNotEmpty() && it.endsWith(";")) {
                _productConfigStr.value = it
                showToast("已粘贴商品配置")
            }
        }
    }

    /**
     * 处理剪贴板文本添加地址对话框
     */
    fun handleClipboardTextAddAddress() {
        val clipboardText = clipboardManager.primaryClip?.getItemAt(0)?.text?.toString()
        // 尝试解密剪贴板内容
        val decryptedText = clipboardText?.let { EncryptionUtils.decrypt(it) }
        Log.d("OrderViewModel", "decryptedText: $decryptedText")
        decryptedText?.let {
            if (it.isNotEmpty()) {
                val segments = it.split(";")
                if (segments.size == 4) {
                    _addAddressStr.value = segments[0]
                    _addAddressLocationStr.value = segments[1]
                    _randomDetailsRegex.value = segments[2]
                    showToast("已粘贴地址配置")
                }
            }
        }
    }

    /**
     * 添加订单Token
     * @param line 剪贴板内容
     */
    fun addOrderToken(
        line: String
    ) {
        viewModelScope.launch {
            try {
                // 解析token获取accessToken和refreshToken
                val parsedToken = TokenParserUtils.parseTokenFromLine(line, "order")
                if (parsedToken != null && parsedToken is OrderTokenEntity) {
                    // 保存到数据库
                    val (success, message) = tokenRepository.insertToken(parsedToken)

                    // 显示结果
                    if (success) {
                        showToast("账号添加成功")
                    } else {
                        showToast("账号添加错误: $message")
                    }
                } else {
                    showToast("Token解析错误")
                }
            } catch (e: Exception) {
                showToast("添加账号异常: ${e.message}")
                Log.e("OrderViewModel", "Error adding order token", e)
            }
        }
    }

    /**
     * 保存当前账号的缓存数据
     * 在开始加载新Token前，保存当前的账号的缓存数据以便下次加载时快速载入
     */
    private fun saveCurrentTokenCacheData(): Boolean {
        // 如果当前没有选中的账号，则不进行保存
        if (currentSelectedAccount.value == null) return false

        // 使用OrderViewModelCacheHelper保存当前账号的缓存数据
        return OrderViewModelCacheHelper.saveCurrentTokenCacheData(this)
    }

    /**
     * 设置当前操作的账号索引
     * @param index 账号索引
     */
    fun setCurrentOperationOrderTokenIndex(index: Int) {
        _currentOperationOrderTokenIndex.value = index
    }

    /**
     * 保存抢券配置
     * @param config 抢券配置JSON字符串
     */
    fun saveCouponGrabbingConfig(config: String) {
        try {
            _couponGrabbingConfig.value = config
            Log.d("OrderViewModel", "Coupon grabbing config saved: $config")
            processRecorder?.recordProcess("抢券配置保存成功")
            showToast("抢券配置已保存")
        } catch (e: Exception) {
            Log.e("OrderViewModel", "Error saving coupon grabbing config", e)
            processRecorder?.recordProcess("抢券配置保存错误: ${e.message}", "ERROR")
            showToast("保存抢券配置错误: ${e.message}")
        }
    }
}

/**
 * 根据首页响应数据确定账户类型
 * @param homepageResponse 首页响应数据
 * @return 账户类型字符串
 */
fun determineAccountType(homepageResponse: HomepageResponse): String {
    val floors = homepageResponse.data?.floors ?: return "未知"
    if (floors.isEmpty()) return "未知"

    val firstFloor = floors[0]
    return when (val assemblyRemark = firstFloor.assemblyremark) {
        "轮播" -> "非新人"
        "新人专区" -> {
            // 处理value可能是HomepageValue对象或数组的情况
            when (val value = firstFloor.value) {
                is Map<*, *> -> {
                    val minimumPriceText =
                        (value as? Map<*, *>)?.get("minimumPriceText") as? String
                    if (minimumPriceText == "0.01元起") "新人特权" else "领居都在买"
                }

                is List<*> -> "领居都在买" // 当value是数组时的默认处理
                else -> "未知类型"
            }
        }

        else -> assemblyRemark
    }
}
